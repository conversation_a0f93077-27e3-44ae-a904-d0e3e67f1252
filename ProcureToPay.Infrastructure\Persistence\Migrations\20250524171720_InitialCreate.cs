﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using ProcureToPay.Domain.ValueObjects;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace ProcureToPay.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "public");

            migrationBuilder.CreateTable(
                name: "asp_net_roles",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    normalized_name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    concurrency_stamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_asp_net_roles", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_users",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    user_name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    normalized_user_name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    normalized_email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    email_confirmed = table.Column<bool>(type: "boolean", nullable: false),
                    password_hash = table.Column<string>(type: "text", nullable: true),
                    security_stamp = table.Column<string>(type: "text", nullable: true),
                    concurrency_stamp = table.Column<string>(type: "text", nullable: true),
                    phone_number = table.Column<string>(type: "text", nullable: true),
                    phone_number_confirmed = table.Column<bool>(type: "boolean", nullable: false),
                    two_factor_enabled = table.Column<bool>(type: "boolean", nullable: false),
                    lockout_end = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    lockout_enabled = table.Column<bool>(type: "boolean", nullable: false),
                    access_failed_count = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_asp_net_users", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "budgets",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    fiscal_year = table.Column<int>(type: "integer", nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    end_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    baseline_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    forecast_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    is_rolling_forecast = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    forecast_periods_json = table.Column<string>(type: "jsonb", nullable: true),
                    allocation_rules_json = table.Column<string>(type: "jsonb", nullable: true),
                    workflow_instance_id = table.Column<Guid>(type: "uuid", nullable: true),
                    created_by_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    approved_by_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_budgets", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "categories",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    unspsc_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    parent_category_id = table.Column<Guid>(type: "uuid", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_categories", x => x.id);
                    table.ForeignKey(
                        name: "FK_categories_categories_parent_category_id",
                        column: x => x.parent_category_id,
                        principalSchema: "public",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "customers",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    customer_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    legal_name = table.Column<string>(type: "text", nullable: true),
                    tax_identifier = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    credit_limit_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    credit_limit_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    default_payment_terms = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    default_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    website = table.Column<string>(type: "text", nullable: true),
                    customer_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    billing_street = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    shipping_street = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    primary_contact_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    primary_contact_role = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    primary_contact_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    primary_contact_phone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    assigned_sales_rep_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_customers", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "departments",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_departments", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "document_link",
                columns: table => new
                {
                    name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    url = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    document_type = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "procurement_workflows",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    workflow_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    description = table.Column<string>(type: "text", maxLength: 500, nullable: true),
                    subject_document_id = table.Column<Guid>(type: "uuid", nullable: false),
                    subject_document_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    workflow_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    version = table.Column<int>(type: "integer", maxLength: 20, nullable: true),
                    current_status = table.Column<int>(type: "integer", nullable: false),
                    started_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    completed_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    initiated_by_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    initiated_by_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_procurement_workflows", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "projects",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    project_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    end_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    BudgetAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    BudgetCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_projects", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "sales_territories",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    code = table.Column<string>(type: "text", nullable: true),
                    territory_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    parent_territory_id = table.Column<Guid>(type: "uuid", nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    primary_salesperson_id = table.Column<string>(type: "text", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_territories", x => x.id);
                    table.ForeignKey(
                        name: "FK_sales_territories_sales_territories_parent_territory_id",
                        column: x => x.parent_territory_id,
                        principalTable: "sales_territories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "tenant_products",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    sku = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    price = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tenant_products", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "tenants",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    identifier = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    subscription_plan = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    contact_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    settings = table.Column<string>(type: "jsonb", nullable: true),
                    address_line1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tenants", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "test_entities",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    test_number = table.Column<int>(type: "integer", nullable: false),
                    created_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_test_entities", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "vendors",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    vendor_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    vat_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    commercial_registration_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    tax_id = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    contact_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    contact_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    phone_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    website = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PrimaryAddress_Street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PrimaryAddress_City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrimaryAddress_State = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrimaryAddress_Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PrimaryAddress_PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vendors", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_role_claims",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    role_id = table.Column<string>(type: "text", nullable: false),
                    claim_type = table.Column<string>(type: "text", nullable: true),
                    claim_value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_asp_net_role_claims", x => x.id);
                    table.ForeignKey(
                        name: "FK_asp_net_role_claims_asp_net_roles_role_id",
                        column: x => x.role_id,
                        principalTable: "asp_net_roles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_claims",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    user_id = table.Column<string>(type: "text", nullable: false),
                    claim_type = table.Column<string>(type: "text", nullable: true),
                    claim_value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_asp_net_user_claims", x => x.id);
                    table.ForeignKey(
                        name: "FK_asp_net_user_claims_asp_net_users_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_logins",
                columns: table => new
                {
                    login_provider = table.Column<string>(type: "text", nullable: false),
                    provider_key = table.Column<string>(type: "text", nullable: false),
                    provider_display_name = table.Column<string>(type: "text", nullable: true),
                    user_id = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_asp_net_user_logins", x => new { x.login_provider, x.provider_key });
                    table.ForeignKey(
                        name: "FK_asp_net_user_logins_asp_net_users_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_roles",
                columns: table => new
                {
                    user_id = table.Column<string>(type: "text", nullable: false),
                    role_id = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_asp_net_user_roles", x => new { x.user_id, x.role_id });
                    table.ForeignKey(
                        name: "FK_asp_net_user_roles_asp_net_roles_role_id",
                        column: x => x.role_id,
                        principalTable: "asp_net_roles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_asp_net_user_roles_asp_net_users_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_tokens",
                columns: table => new
                {
                    user_id = table.Column<string>(type: "text", nullable: false),
                    login_provider = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_asp_net_user_tokens", x => new { x.user_id, x.login_provider, x.name });
                    table.ForeignKey(
                        name: "FK_asp_net_user_tokens_asp_net_users_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_definitions",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    description = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    sku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    gtin = table.Column<string>(type: "character varying(14)", maxLength: 14, nullable: true),
                    upc = table.Column<string>(type: "character varying(12)", maxLength: 12, nullable: true),
                    ean = table.Column<string>(type: "character varying(13)", maxLength: 13, nullable: true),
                    lifecycle_state = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Active"),
                    category_id = table.Column<Guid>(type: "uuid", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    attributes_json = table.Column<string>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_definitions", x => x.id);
                    table.ForeignKey(
                        name: "FK_product_definitions_categories_category_id",
                        column: x => x.category_id,
                        principalSchema: "public",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "products",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    product_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    unit_of_measure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    is_active = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    category_id = table.Column<Guid>(type: "uuid", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_products", x => x.id);
                    table.ForeignKey(
                        name: "FK_products_categories_category_id",
                        column: x => x.category_id,
                        principalSchema: "public",
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "budget_allocations",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    budget_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    department_id = table.Column<Guid>(type: "uuid", nullable: false),
                    allocated_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    allocated_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    consumed_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    consumed_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    allocation_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    fiscal_period_identifier = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_budget_allocations", x => x.id);
                    table.ForeignKey(
                        name: "FK_budget_allocations_budgets_budget_id",
                        column: x => x.budget_id,
                        principalSchema: "public",
                        principalTable: "budgets",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_budget_allocations_departments_department_id",
                        column: x => x.department_id,
                        principalSchema: "public",
                        principalTable: "departments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "procurement_workflow_steps",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    workflow_id = table.Column<Guid>(type: "uuid", nullable: false),
                    procurement_workflow_id = table.Column<Guid>(type: "uuid", nullable: false),
                    step_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    sequence_order = table.Column<int>(type: "integer", nullable: false),
                    step_order = table.Column<int>(type: "integer", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    assignee_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    approver_role_id = table.Column<Guid>(type: "uuid", nullable: true),
                    approver_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    assignee_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    assigned_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    action_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    comments = table.Column<string>(type: "text", nullable: true),
                    condition_expression = table.Column<string>(type: "text", nullable: true),
                    sla_duration = table.Column<TimeSpan>(type: "interval", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_procurement_workflow_steps", x => x.id);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_asp_net_users_approver_user_id",
                        column: x => x.approver_user_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_procurement_workflows_procuremen~",
                        column: x => x.procurement_workflow_id,
                        principalTable: "procurement_workflows",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_procurement_workflow_steps_procurement_workflows_workflow_id",
                        column: x => x.workflow_id,
                        principalTable: "procurement_workflows",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "requests_for_information",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    rfi_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    issued_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    issue_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    response_due_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    response_deadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    project_id = table.Column<Guid>(type: "uuid", nullable: true),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    issued_by_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    issued_by_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    target_audience_description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_requests_for_information", x => x.id);
                    table.ForeignKey(
                        name: "FK_requests_for_information_projects_project_id",
                        column: x => x.project_id,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "sales_territory_representatives",
                columns: table => new
                {
                    sales_territory_id = table.Column<Guid>(type: "uuid", nullable: false),
                    representative_id = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_territory_representatives", x => new { x.sales_territory_id, x.representative_id });
                    table.ForeignKey(
                        name: "FK_sales_territory_representatives_asp_net_users_representativ~",
                        column: x => x.representative_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_sales_territory_representatives_sales_territories_sales_ter~",
                        column: x => x.sales_territory_id,
                        principalTable: "sales_territories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "contracts",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    contract_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    title = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    contract_type = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    end_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TotalContractValueAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    TotalContractValueCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    payment_terms = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    renewal_terms = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    is_auto_renew = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    termination_penalty_terms = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    terms_and_conditions = table.Column<string>(type: "text", nullable: true),
                    milestones_json = table.Column<string>(type: "jsonb", nullable: true),
                    sla_details_json = table.Column<string>(type: "jsonb", nullable: true),
                    compliance_document_links_json = table.Column<string>(type: "jsonb", nullable: true),
                    vendor_performance_score_snapshot = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_contracts", x => x.id);
                    table.ForeignKey(
                        name: "FK_contracts_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "suppliers",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    supplier_name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Active"),
                    risk_rating = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    sustainability_score = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    is_contract_manufacturer = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    average_lead_time_days = table.Column<int>(type: "integer", nullable: true),
                    is_csr_compliant = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    capacity_utilization_percent = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EmergencyContacts = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_suppliers", x => x.id);
                    table.ForeignKey(
                        name: "FK_suppliers_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "vendor_products",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_definition_id = table.Column<Guid>(type: "uuid", nullable: false),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    vendor_sku = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    UnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitPriceCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    unit_of_measure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    pack_size = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false),
                    lead_time_days = table.Column<int>(type: "integer", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vendor_products", x => x.id);
                    table.ForeignKey(
                        name: "FK_vendor_products_product_definitions_product_definition_id",
                        column: x => x.product_definition_id,
                        principalTable: "product_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_vendor_products_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "requests_for_proposal",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    rfp_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    scope_of_work = table.Column<string>(type: "text", nullable: true),
                    evaluation_criteria = table.Column<string>(type: "jsonb", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    issued_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    issue_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    decision_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    project_id = table.Column<Guid>(type: "uuid", nullable: true),
                    question_deadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    submission_deadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    issued_by_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    issued_by_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    department = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    expected_contract_start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    expected_contract_duration = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    awarded_vendor_id = table.Column<Guid>(type: "uuid", nullable: true),
                    awarded_contract_id = table.Column<Guid>(type: "uuid", nullable: true),
                    completed_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_requests_for_proposal", x => x.id);
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_contracts_awarded_contract_id",
                        column: x => x.awarded_contract_id,
                        principalTable: "contracts",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_projects_project_id",
                        column: x => x.project_id,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_requests_for_proposal_vendors_awarded_vendor_id",
                        column: x => x.awarded_vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id");
                });

            migrationBuilder.CreateTable(
                name: "vendor_proposals",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    request_for_proposal_id = table.Column<Guid>(type: "uuid", nullable: false),
                    submission_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    total_proposed_value = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    validity_period_days = table.Column<int>(type: "integer", nullable: false),
                    validity_end_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    alternate_payment_terms = table.Column<string>(type: "text", nullable: true),
                    value_added_services = table.Column<string>(type: "text", nullable: true),
                    sustainability_commitments = table.Column<string>(type: "text", nullable: true),
                    risk_sharing_clauses = table.Column<string>(type: "text", nullable: true),
                    performance_guarantees = table.Column<string>(type: "text", nullable: true),
                    subcontractor_disclosures = table.Column<string>(type: "text", nullable: true),
                    compliance_certifications_json = table.Column<string>(type: "text", nullable: true),
                    comments = table.Column<string>(type: "text", nullable: true),
                    version = table.Column<int>(type: "integer", nullable: false),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vendor_proposals", x => x.id);
                    table.ForeignKey(
                        name: "FK_vendor_proposals_requests_for_proposal_request_for_proposal~",
                        column: x => x.request_for_proposal_id,
                        principalTable: "requests_for_proposal",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_vendor_proposals_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "delivery_note_lines",
                columns: table => new
                {
                    delivery_note_id = table.Column<Guid>(type: "uuid", nullable: false),
                    line_number = table.Column<int>(type: "integer", nullable: false),
                    purchase_order_line_id = table.Column<Guid>(type: "uuid", nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sales_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    sales_order_line_number = table.Column<int>(type: "integer", nullable: true),
                    product_sku_snapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    quantity_shipped = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    unit_of_measure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    batch_number = table.Column<string>(type: "text", maxLength: 100, nullable: true),
                    serial_number = table.Column<string>(type: "text", maxLength: 100, nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_delivery_note_lines", x => new { x.delivery_note_id, x.line_number });
                    table.ForeignKey(
                        name: "FK_delivery_note_lines_product_definitions_product_id",
                        column: x => x.product_id,
                        principalTable: "product_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "delivery_notes",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    delivery_note_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    purchase_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sales_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    shipment_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    delivery_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    received_by = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: true),
                    carrier_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    tracking_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    billing_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_delivery_notes", x => x.id);
                    table.ForeignKey(
                        name: "FK_delivery_notes_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "goods_receipt_note_lines",
                columns: table => new
                {
                    line_number = table.Column<int>(type: "integer", nullable: false),
                    goods_receipt_note_id = table.Column<Guid>(type: "uuid", nullable: false),
                    purchase_order_line_id = table.Column<Guid>(type: "uuid", nullable: false),
                    delivery_note_line_id = table.Column<Guid>(type: "uuid", nullable: true),
                    delivery_note_line_delivery_note_id = table.Column<Guid>(type: "uuid", nullable: true),
                    delivery_note_line_line_number = table.Column<int>(type: "integer", nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_sku_snapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    product_description_snapshot = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    quantity_received = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    unit_of_measure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    quality_control_status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    put_away_location = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    batch_number = table.Column<string>(type: "text", nullable: true),
                    lot_number = table.Column<string>(type: "text", nullable: true),
                    expiry_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    inspection_completed = table.Column<bool>(type: "boolean", nullable: false),
                    quantity_accepted = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    quantity_rejected = table.Column<decimal>(type: "numeric(18,4)", nullable: false, defaultValue: 0m),
                    rejection_reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_goods_receipt_note_lines", x => new { x.goods_receipt_note_id, x.line_number });
                    table.ForeignKey(
                        name: "FK_goods_receipt_note_lines_delivery_note_lines_delivery_note_~",
                        columns: x => new { x.delivery_note_line_delivery_note_id, x.delivery_note_line_line_number },
                        principalTable: "delivery_note_lines",
                        principalColumns: new[] { "delivery_note_id", "line_number" });
                    table.ForeignKey(
                        name: "FK_goods_receipt_note_lines_product_definitions_product_id",
                        column: x => x.product_id,
                        principalTable: "product_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "goods_receipt_notes",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    goods_receipt_note_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    grn_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    purchase_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    delivery_note_id = table.Column<Guid>(type: "uuid", nullable: true),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    receipt_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    inspection_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    receiving_location = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    received_by_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    received_by_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    notes = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_goods_receipt_notes", x => x.id);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_asp_net_users_received_by_user_id",
                        column: x => x.received_by_user_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_delivery_notes_delivery_note_id",
                        column: x => x.delivery_note_id,
                        principalTable: "delivery_notes",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_goods_receipt_notes_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "invoice_lines",
                columns: table => new
                {
                    line_number = table.Column<int>(type: "integer", nullable: false),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: false),
                    purchase_order_line_id = table.Column<Guid>(type: "uuid", nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: true),
                    description = table.Column<string>(type: "text", maxLength: 1000, nullable: false),
                    quantity = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    unit_price = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    line_total = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    tax_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: true, defaultValue: 0m),
                    tax_rate = table.Column<decimal>(type: "numeric(5,4)", nullable: true),
                    unit_of_measure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    notes = table.Column<string>(type: "text", nullable: true),
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_invoice_lines", x => new { x.invoice_id, x.line_number });
                    table.ForeignKey(
                        name: "FK_invoice_lines_product_definitions_product_id",
                        column: x => x.product_id,
                        principalTable: "product_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "invoices",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    invoice_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    customer_id = table.Column<Guid>(type: "uuid", nullable: true),
                    purchase_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    invoice_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    due_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    sub_total = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    subtotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    tax_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    total_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    amount_paid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    payment_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    payment_terms = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_invoices", x => x.id);
                    table.ForeignKey(
                        name: "FK_invoices_customers_customer_id",
                        column: x => x.customer_id,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_invoices_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "payment_transactions",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    transaction_reference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    payment_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    payment_method = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    bank_reference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_payment_transactions", x => x.id);
                    table.ForeignKey(
                        name: "FK_payment_transactions_invoices_invoice_id",
                        column: x => x.invoice_id,
                        principalTable: "invoices",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_payment_transactions_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "purchase_order_lines",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    purchase_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    vendor_product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sku_snapshot = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description_snapshot = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    UnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    UnitPriceCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    unit_of_measure_snapshot = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    LineTotalAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    LineTotalCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_order_lines", x => x.id);
                    table.ForeignKey(
                        name: "FK_purchase_order_lines_vendor_products_vendor_product_id",
                        column: x => x.vendor_product_id,
                        principalTable: "vendor_products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "technical_submittals",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    submittal_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    revision = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    submittal_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    current_review_cycle = table.Column<int>(type: "integer", nullable: false),
                    review_due_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    required_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    review_start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    review_completion_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    current_overall_disposition = table.Column<int>(type: "integer", nullable: false),
                    purchase_order_line_id = table.Column<Guid>(type: "uuid", nullable: true),
                    contract_id = table.Column<Guid>(type: "uuid", nullable: true),
                    project_id = table.Column<Guid>(type: "uuid", nullable: false),
                    specification_section = table.Column<string>(type: "text", nullable: true),
                    specification_id = table.Column<Guid>(type: "uuid", nullable: true),
                    related_itp_reference = table.Column<string>(type: "text", nullable: true),
                    test_plan_id = table.Column<Guid>(type: "uuid", nullable: true),
                    related_ncr_reference = table.Column<string>(type: "text", nullable: true),
                    non_conformance_report_id = table.Column<Guid>(type: "uuid", nullable: true),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: true),
                    submitted_by_id = table.Column<string>(type: "text", nullable: false),
                    submitted_by_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    current_reviewer_id = table.Column<Guid>(type: "uuid", nullable: true),
                    cycle_count = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    is_as_built = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    is_final_documentation = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    submitted_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    final_sign_off_by_id = table.Column<string>(type: "text", nullable: true),
                    signed_off_by_id = table.Column<Guid>(type: "uuid", nullable: true),
                    final_sign_off_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    signed_off_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    resubmission_count = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    max_resubmissions = table.Column<int>(type: "integer", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    submitted_documents = table.Column<List<DocumentLink>>(type: "jsonb", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_technical_submittals", x => x.id);
                    table.ForeignKey(
                        name: "FK_technical_submittals_asp_net_users_submitted_by_user_id",
                        column: x => x.submitted_by_user_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_technical_submittals_contracts_contract_id",
                        column: x => x.contract_id,
                        principalTable: "contracts",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "FK_technical_submittals_projects_project_id",
                        column: x => x.project_id,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_technical_submittals_purchase_order_lines_purchase_order_li~",
                        column: x => x.purchase_order_line_id,
                        principalTable: "purchase_order_lines",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "FK_technical_submittals_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "submittal_reviews",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    technical_submittal_id = table.Column<Guid>(type: "uuid", nullable: false),
                    review_cycle = table.Column<int>(type: "integer", nullable: false),
                    reviewer_id = table.Column<string>(type: "text", nullable: false),
                    reviewer_guid = table.Column<Guid>(type: "uuid", nullable: false),
                    reviewer_name = table.Column<string>(type: "text", nullable: false),
                    review_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    disposition = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    comments = table.Column<string>(type: "text", nullable: true),
                    markup_document = table.Column<DocumentLink>(type: "jsonb", nullable: true),
                    technical_submittal_id1 = table.Column<Guid>(type: "uuid", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_submittal_reviews", x => x.id);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_asp_net_users_reviewer_id",
                        column: x => x.reviewer_id,
                        principalTable: "asp_net_users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_technical_submittals_technical_submittal_~",
                        column: x => x.technical_submittal_id,
                        principalTable: "technical_submittals",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_submittal_reviews_technical_submittals_technical_submittal~1",
                        column: x => x.technical_submittal_id1,
                        principalTable: "technical_submittals",
                        principalColumn: "id");
                });

            migrationBuilder.CreateTable(
                name: "purchase_orders",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    order_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    delivery_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    payment_terms = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    vendor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    contract_id = table.Column<Guid>(type: "uuid", nullable: true),
                    requisition_id = table.Column<Guid>(type: "uuid", nullable: true),
                    Shipment_Street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Shipment_City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Shipment_State = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Shipment_Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Shipment_PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_orders", x => x.id);
                    table.ForeignKey(
                        name: "FK_purchase_orders_contracts_contract_id",
                        column: x => x.contract_id,
                        principalTable: "contracts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_purchase_orders_vendors_vendor_id",
                        column: x => x.vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "purchase_requisitions",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    requisition_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    requestor_name = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    requestor_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: false),
                    requestor_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    department = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    request_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    date_needed = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    justification = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    TotalEstimatedCostAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    Shipping_Street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Shipping_City = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Shipping_State = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Shipping_Country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Shipping_PostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    associated_purchase_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_requisitions", x => x.id);
                    table.ForeignKey(
                        name: "FK_purchase_requisitions_purchase_orders_associated_purchase_o~",
                        column: x => x.associated_purchase_order_id,
                        principalTable: "purchase_orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "purchase_requisition_lines",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    purchase_requisition_id = table.Column<Guid>(type: "uuid", nullable: false),
                    line_number = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    vendor_product_id = table.Column<Guid>(type: "uuid", nullable: true),
                    product_definition_id = table.Column<Guid>(type: "uuid", nullable: true),
                    description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    unit_of_measure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EstUnitPriceAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    EstUnitPriceCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    EstLineCostAmount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    EstLineCostCurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    gl_account_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    date_needed = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    suggested_vendor_id = table.Column<Guid>(type: "uuid", nullable: true),
                    notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_purchase_requisition_lines", x => x.id);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_product_definitions_product_defi~",
                        column: x => x.product_definition_id,
                        principalTable: "product_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_purchase_requisitions_purchase_r~",
                        column: x => x.purchase_requisition_id,
                        principalTable: "purchase_requisitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_vendor_products_vendor_product_id",
                        column: x => x.vendor_product_id,
                        principalTable: "vendor_products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_purchase_requisition_lines_vendors_suggested_vendor_id",
                        column: x => x.suggested_vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "request_for_quotes",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    rfq_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    title = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    submission_deadline = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    required_delivery_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    scope_of_work = table.Column<string>(type: "text", nullable: true),
                    terms_and_conditions = table.Column<string>(type: "text", nullable: true),
                    vendor_reference_instructions = table.Column<string>(type: "text", nullable: true),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    deliver_to_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    deliver_to_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    deliver_to_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    created_by_user_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    originating_requisition_id = table.Column<Guid>(type: "uuid", nullable: true),
                    awarded_vendor_id = table.Column<Guid>(type: "uuid", nullable: true),
                    related_agreement_id = table.Column<Guid>(type: "uuid", nullable: true),
                    contact_person_email = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    communication_method = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_request_for_quotes", x => x.id);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_contracts_related_agreement_id",
                        column: x => x.related_agreement_id,
                        principalTable: "contracts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_purchase_requisitions_originating_requis~",
                        column: x => x.originating_requisition_id,
                        principalTable: "purchase_requisitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quotes_vendors_awarded_vendor_id",
                        column: x => x.awarded_vendor_id,
                        principalTable: "vendors",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "request_for_quote_lines",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    request_for_quote_id = table.Column<Guid>(type: "uuid", nullable: false),
                    line_number = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_definition_id = table.Column<Guid>(type: "uuid", nullable: true),
                    vendor_product_id = table.Column<Guid>(type: "uuid", nullable: true),
                    description = table.Column<string>(type: "text", nullable: false),
                    quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    unit_of_measure = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    target_unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    target_unit_price_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    alternate_item_proposal = table.Column<string>(type: "text", nullable: true),
                    est_tco_value_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    est_tco_value_currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    technical_specifications = table.Column<string>(type: "text", nullable: true),
                    sample_required = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    minimum_order_quantity = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: true),
                    preferred_incoterm = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    is_substitute_allowed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    notes = table.Column<string>(type: "text", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_request_for_quote_lines", x => x.id);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_product_definitions_product_definit~",
                        column: x => x.product_definition_id,
                        principalTable: "product_definitions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_request_for_quotes_request_for_quot~",
                        column: x => x.request_for_quote_id,
                        principalSchema: "public",
                        principalTable: "request_for_quotes",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_request_for_quote_lines_vendor_products_vendor_product_id",
                        column: x => x.vendor_product_id,
                        principalTable: "vendor_products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "return_authorization_lines",
                columns: table => new
                {
                    line_number = table.Column<int>(type: "integer", nullable: false),
                    return_authorization_id = table.Column<Guid>(type: "uuid", nullable: false),
                    original_sales_order_line_id = table.Column<Guid>(type: "uuid", nullable: true),
                    sales_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    sales_order_line_number = table.Column<int>(type: "integer", nullable: true),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: true),
                    invoice_line_number = table.Column<int>(type: "integer", nullable: true),
                    item_condition = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    product_definition_id = table.Column<Guid>(type: "uuid", nullable: true),
                    vendor_product_id = table.Column<Guid>(type: "uuid", nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sku_snapshot = table.Column<string>(type: "text", nullable: false),
                    description_snapshot = table.Column<string>(type: "text", nullable: false),
                    quantity_authorized = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    unit_of_measure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    quantity_received = table.Column<decimal>(type: "numeric", nullable: false),
                    reason_for_return = table.Column<string>(type: "text", nullable: false),
                    requested_action = table.Column<int>(type: "integer", nullable: false),
                    original_sales_order_line_sales_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    original_sales_order_line_line_number = table.Column<int>(type: "integer", nullable: true),
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_return_authorization_lines", x => new { x.return_authorization_id, x.line_number });
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_invoice_lines_invoice_id_invoice~",
                        columns: x => new { x.invoice_id, x.invoice_line_number },
                        principalTable: "invoice_lines",
                        principalColumns: new[] { "invoice_id", "line_number" },
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_product_definitions_product_defi~",
                        column: x => x.product_definition_id,
                        principalTable: "product_definitions",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "public",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorization_lines_vendor_products_vendor_product_id",
                        column: x => x.vendor_product_id,
                        principalTable: "vendor_products",
                        principalColumn: "id");
                });

            migrationBuilder.CreateTable(
                name: "return_authorizations",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    rma_number = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    request_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    customer_id = table.Column<Guid>(type: "uuid", nullable: false),
                    original_sales_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sales_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: true),
                    authorization_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    expiry_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    reason_for_return = table.Column<string>(type: "text", nullable: true),
                    requested_action = table.Column<int>(type: "integer", nullable: false),
                    shipping_instructions = table.Column<string>(type: "text", nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_return_authorizations", x => x.id);
                    table.ForeignKey(
                        name: "FK_return_authorizations_customers_customer_id",
                        column: x => x.customer_id,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_return_authorizations_invoices_invoice_id",
                        column: x => x.invoice_id,
                        principalTable: "invoices",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "sales_orders",
                schema: "public",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_number = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    order_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    customer_id = table.Column<Guid>(type: "uuid", nullable: true),
                    status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    billing_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    billing_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    shipping_street = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    shipping_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_state = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    shipping_postal_code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    total_amount = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    currency_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    is_credit_approved = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    atp_check_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    is_atp_confirmed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    is_drop_shipment = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    salesperson_id = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    commission_rate = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    related_return_authorization_id = table.Column<Guid>(type: "uuid", nullable: true),
                    sales_territory_id = table.Column<Guid>(type: "uuid", nullable: true),
                    edi_transaction_reference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    sales_territory_id1 = table.Column<Guid>(type: "uuid", nullable: true),
                    xmin = table.Column<uint>(type: "xid", rowVersion: true, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_orders", x => x.id);
                    table.ForeignKey(
                        name: "FK_sales_orders_customers_customer_id",
                        column: x => x.customer_id,
                        principalSchema: "public",
                        principalTable: "customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_sales_orders_return_authorizations_related_return_authoriza~",
                        column: x => x.related_return_authorization_id,
                        principalTable: "return_authorizations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_orders_sales_territories_sales_territory_id",
                        column: x => x.sales_territory_id,
                        principalTable: "sales_territories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_orders_sales_territories_sales_territory_id1",
                        column: x => x.sales_territory_id1,
                        principalTable: "sales_territories",
                        principalColumn: "id");
                });

            migrationBuilder.CreateTable(
                name: "sales_order_lines",
                columns: table => new
                {
                    sales_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    line_number = table.Column<int>(type: "integer", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    vendor_product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sku_snapshot = table.Column<string>(type: "text", nullable: false),
                    description_snapshot = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    requested_delivery_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantity = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    unit_of_measure = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    unit_price_amount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    unit_price_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    applied_discount_description = table.Column<string>(type: "text", nullable: true),
                    discount_amount_value = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    discount_amount_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    line_total_amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    line_total_currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    quantity_backordered = table.Column<decimal>(type: "numeric", nullable: false),
                    reserved_serial_numbers_json = table.Column<string>(type: "text", nullable: true),
                    warranty_end_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_kit_component = table.Column<bool>(type: "boolean", nullable: false),
                    parent_sales_order_line_id = table.Column<Guid>(type: "uuid", nullable: true),
                    project_id = table.Column<Guid>(type: "uuid", nullable: true),
                    cost_code = table.Column<string>(type: "text", nullable: true),
                    is_deleted = table.Column<bool>(type: "boolean", nullable: false),
                    parent_sales_order_line_sales_order_id = table.Column<Guid>(type: "uuid", nullable: true),
                    parent_sales_order_line_line_number = table.Column<int>(type: "integer", nullable: true),
                    project_id1 = table.Column<Guid>(type: "uuid", nullable: true),
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sales_order_lines", x => new { x.sales_order_id, x.line_number });
                    table.ForeignKey(
                        name: "FK_sales_order_lines_products_product_id",
                        column: x => x.product_id,
                        principalSchema: "public",
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_projects_project_id",
                        column: x => x.project_id,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_projects_project_id1",
                        column: x => x.project_id1,
                        principalSchema: "public",
                        principalTable: "projects",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "FK_sales_order_lines_sales_order_lines_parent_sales_order_line~",
                        columns: x => new { x.parent_sales_order_line_sales_order_id, x.parent_sales_order_line_line_number },
                        principalTable: "sales_order_lines",
                        principalColumns: new[] { "sales_order_id", "line_number" });
                    table.ForeignKey(
                        name: "FK_sales_order_lines_sales_orders_sales_order_id",
                        column: x => x.sales_order_id,
                        principalSchema: "public",
                        principalTable: "sales_orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_sales_order_lines_vendor_products_vendor_product_id",
                        column: x => x.vendor_product_id,
                        principalTable: "vendor_products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "categories",
                columns: new[] { "id", "code", "created_at", "description", "modified_at", "name", "parent_category_id", "tenant_id", "unspsc_code" },
                values: new object[,]
                {
                    { new Guid("*************-2222-2222-************"), "OFF-SUP", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "General office supplies and stationery", null, "Office Supplies", null, new Guid("********-1111-1111-1111-********1111"), "14111500" },
                    { new Guid("*************-2222-2222-************"), "IT-EQP", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Computer hardware and accessories", null, "IT Equipment", null, new Guid("********-1111-1111-1111-********1111"), "43210000" },
                    { new Guid("*************-2222-2222-************"), "FURN", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Office furniture and fixtures", null, "Furniture", null, new Guid("********-1111-1111-1111-********1111"), "56100000" },
                    { new Guid("*************-2222-2222-************"), "RAW-MAT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Materials used in manufacturing", null, "Raw Materials", null, new Guid("********-1111-1111-1111-********1111"), "11000000" },
                    { new Guid("*************-2222-2222-************"), "SERV", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Professional and consulting services", null, "Services", null, new Guid("********-1111-1111-1111-********1111"), "80000000" }
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "departments",
                columns: new[] { "id", "code", "created_at", "description", "modified_at", "name", "tenant_id" },
                values: new object[,]
                {
                    { new Guid("********-4444-4444-4444-********4401"), "IT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Handles IT infrastructure, software development, and technical support.", null, "Information Technology", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4402"), "FIN", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Manages financial operations, budgeting, and accounting.", null, "Finance", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4403"), "HR", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Handles employee recruitment, training, and personnel management.", null, "Human Resources", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4404"), "PROC", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Manages purchasing, vendor relationships, and supply chain operations.", null, "Procurement", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4405"), "OPS", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Oversees day-to-day operational activities and logistics.", null, "Operations", new Guid("********-1111-1111-1111-********1111") },
                    { new Guid("********-4444-4444-4444-********4406"), "MKT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Handles advertising, brand management, and market research.", null, "Marketing", new Guid("********-1111-1111-1111-********1111") }
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "tenants",
                columns: new[] { "id", "address_line1", "city", "contact_email", "country", "created_at", "identifier", "is_active", "modified_at", "name", "postal_code", "settings", "subscription_plan" },
                values: new object[] { new Guid("********-1111-1111-1111-********1111"), null, null, "<EMAIL>", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "system", true, null, "Default System Tenant", null, null, "Standard" });

            migrationBuilder.InsertData(
                schema: "public",
                table: "categories",
                columns: new[] { "id", "code", "created_at", "description", "modified_at", "name", "parent_category_id", "tenant_id", "unspsc_code" },
                values: new object[,]
                {
                    { new Guid("*************-2222-2222-************"), "PAPER", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Paper, notebooks, and notepads", null, "Paper Products", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "14111500" },
                    { new Guid("*************-2222-2222-************"), "WRITE", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Pens, pencils, and markers", null, "Writing Instruments", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "44121700" },
                    { new Guid("*************-2222-2222-************"), "COMP", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Desktops, laptops, and tablets", null, "Computers", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "43211500" },
                    { new Guid("*************-2222-2222-************"), "PERIPH", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Monitors, keyboards, and mice", null, "Peripherals", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "43211900" },
                    { new Guid("*************-2222-2222-************"), "CONSULT", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Business and management consulting", null, "Consulting", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "80101500" },
                    { new Guid("*************-2222-2222-************"), "IT-SERV", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Software development and support", null, "IT Services", new Guid("*************-2222-2222-************"), new Guid("********-1111-1111-1111-********1111"), "81110000" }
                });

            migrationBuilder.InsertData(
                schema: "public",
                table: "products",
                columns: new[] { "id", "category_id", "created_at", "description", "is_active", "modified_at", "name", "product_code", "tenant_id", "unit_of_measure" },
                values: new object[,]
                {
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Premium A4 copy paper, 80gsm, 500 sheets per ream, 5 reams per box", true, null, "A4 Copy Paper", "PAPER-001", new Guid("********-1111-1111-1111-********1111"), "Box" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "A5 spiral-bound notebook, 100 pages, ruled", true, null, "Spiral Notebook", "PAPER-002", new Guid("********-1111-1111-1111-********1111"), "Each" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Blue ballpoint pens, medium point, 12 pens per box", true, null, "Ballpoint Pen", "WRITE-001", new Guid("********-1111-1111-1111-********1111"), "Box" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "15.6\" business laptop, 16GB RAM, 512GB SSD, Intel Core i7", true, null, "Laptop Computer", "COMP-001", new Guid("********-1111-1111-1111-********1111"), "Each" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "24-inch LED monitor, 1080p, HDMI and DisplayPort", true, null, "24\" Monitor", "PERIPH-001", new Guid("********-1111-1111-1111-********1111"), "Each" },
                    { new Guid("*************-3333-3333-************"), new Guid("*************-2222-2222-************"), new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Ergonomic wireless mouse, 2.4GHz, USB receiver", true, null, "Wireless Mouse", "PERIPH-002", new Guid("********-1111-1111-1111-********1111"), "Each" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_asp_net_role_claims_role_id",
                table: "asp_net_role_claims",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_roles_normalized_name",
                table: "asp_net_roles",
                column: "normalized_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_asp_net_user_claims_user_id",
                table: "asp_net_user_claims",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_asp_net_user_logins_user_id",
                table: "asp_net_user_logins",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_asp_net_user_roles_role_id",
                table: "asp_net_user_roles",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_users_normalized_email",
                table: "asp_net_users",
                column: "normalized_email");

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_users_normalized_user_name",
                table: "asp_net_users",
                column: "normalized_user_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_budget_id",
                schema: "public",
                table: "budget_allocations",
                column: "budget_id");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_budget_id_fiscal_period_identifier",
                schema: "public",
                table: "budget_allocations",
                columns: new[] { "budget_id", "fiscal_period_identifier" });

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_department_id",
                schema: "public",
                table: "budget_allocations",
                column: "department_id");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_fiscal_period_identifier",
                schema: "public",
                table: "budget_allocations",
                column: "fiscal_period_identifier");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_status",
                schema: "public",
                table: "budget_allocations",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_budget_allocations_tenant_id",
                schema: "public",
                table: "budget_allocations",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_approved_by_id",
                schema: "public",
                table: "budgets",
                column: "approved_by_id");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_created_by_id",
                schema: "public",
                table: "budgets",
                column: "created_by_id");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_end_date",
                schema: "public",
                table: "budgets",
                column: "end_date");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_fiscal_year",
                schema: "public",
                table: "budgets",
                column: "fiscal_year");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_is_deleted",
                schema: "public",
                table: "budgets",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_name",
                schema: "public",
                table: "budgets",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_start_date",
                schema: "public",
                table: "budgets",
                column: "start_date");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_status",
                schema: "public",
                table: "budgets",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_tenant_id",
                schema: "public",
                table: "budgets",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_budgets_workflow_instance_id",
                schema: "public",
                table: "budgets",
                column: "workflow_instance_id");

            migrationBuilder.CreateIndex(
                name: "IX_categories_code_unique",
                schema: "public",
                table: "categories",
                column: "code",
                unique: true,
                filter: "\"code\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_categories_parent_category_id",
                schema: "public",
                table: "categories",
                column: "parent_category_id");

            migrationBuilder.CreateIndex(
                name: "IX_categories_tenant_id",
                schema: "public",
                table: "categories",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_categories_unspsc_code",
                schema: "public",
                table: "categories",
                column: "unspsc_code");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_contract_number",
                table: "contracts",
                column: "contract_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_contracts_end_date",
                table: "contracts",
                column: "end_date");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_is_deleted",
                table: "contracts",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_start_date",
                table: "contracts",
                column: "start_date");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_status",
                table: "contracts",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_tenant_id",
                table: "contracts",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_contracts_vendor_id",
                table: "contracts",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_customers_assigned_sales_rep_id",
                schema: "public",
                table: "customers",
                column: "assigned_sales_rep_id");

            migrationBuilder.CreateIndex(
                name: "IX_customers_customer_type",
                schema: "public",
                table: "customers",
                column: "customer_type");

            migrationBuilder.CreateIndex(
                name: "IX_customers_is_active",
                schema: "public",
                table: "customers",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "IX_customers_is_deleted",
                schema: "public",
                table: "customers",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_customers_name",
                schema: "public",
                table: "customers",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_customers_tax_identifier",
                schema: "public",
                table: "customers",
                column: "tax_identifier");

            migrationBuilder.CreateIndex(
                name: "IX_customers_tenant_id",
                schema: "public",
                table: "customers",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_customers_tenant_id_customer_code",
                schema: "public",
                table: "customers",
                columns: new[] { "tenant_id", "customer_code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_product_id",
                table: "delivery_note_lines",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_purchase_order_line_id",
                table: "delivery_note_lines",
                column: "purchase_order_line_id");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_note_lines_sales_order_id_sales_order_line_number",
                table: "delivery_note_lines",
                columns: new[] { "sales_order_id", "sales_order_line_number" });

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_delivery_date",
                table: "delivery_notes",
                column: "delivery_date");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_delivery_note_number",
                table: "delivery_notes",
                column: "delivery_note_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_purchase_order_id",
                table: "delivery_notes",
                column: "purchase_order_id");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_sales_order_id",
                table: "delivery_notes",
                column: "sales_order_id");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_status",
                table: "delivery_notes",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_tenant_id",
                table: "delivery_notes",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_delivery_notes_vendor_id",
                table: "delivery_notes",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_departments_name",
                schema: "public",
                table: "departments",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_departments_tenant_id",
                schema: "public",
                table: "departments",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_departments_tenant_id_code",
                schema: "public",
                table: "departments",
                columns: new[] { "tenant_id", "code" },
                unique: true,
                filter: "\"Code\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_delivery_note_line_delivery_note_i~",
                table: "goods_receipt_note_lines",
                columns: new[] { "delivery_note_line_delivery_note_id", "delivery_note_line_line_number" });

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_expiry_date",
                table: "goods_receipt_note_lines",
                column: "expiry_date");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_product_id",
                table: "goods_receipt_note_lines",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_purchase_order_line_id",
                table: "goods_receipt_note_lines",
                column: "purchase_order_line_id");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_note_lines_quality_control_status",
                table: "goods_receipt_note_lines",
                column: "quality_control_status");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_delivery_note_id",
                table: "goods_receipt_notes",
                column: "delivery_note_id");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_goods_receipt_note_number",
                table: "goods_receipt_notes",
                column: "goods_receipt_note_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_purchase_order_id",
                table: "goods_receipt_notes",
                column: "purchase_order_id");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_receipt_date",
                table: "goods_receipt_notes",
                column: "receipt_date");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_received_by_user_id",
                table: "goods_receipt_notes",
                column: "received_by_user_id");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_status",
                table: "goods_receipt_notes",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_tenant_id",
                table: "goods_receipt_notes",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_goods_receipt_notes_vendor_id",
                table: "goods_receipt_notes",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_invoice_lines_product_id",
                table: "invoice_lines",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "IX_invoice_lines_purchase_order_line_id",
                table: "invoice_lines",
                column: "purchase_order_line_id");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_customer_id",
                table: "invoices",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_due_date",
                table: "invoices",
                column: "due_date");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_invoice_date",
                table: "invoices",
                column: "invoice_date");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_purchase_order_id",
                table: "invoices",
                column: "purchase_order_id");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_status",
                table: "invoices",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_tenant_id",
                table: "invoices",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_vendor_id",
                table: "invoices",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_invoices_vendor_id_invoice_number",
                table: "invoices",
                columns: new[] { "vendor_id", "invoice_number" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_invoice_id",
                table: "payment_transactions",
                column: "invoice_id");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_payment_date",
                table: "payment_transactions",
                column: "payment_date");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_status",
                table: "payment_transactions",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_tenant_id",
                table: "payment_transactions",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_TransactionReference",
                table: "payment_transactions",
                column: "transaction_reference",
                unique: true,
                filter: "\"transaction_reference\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_payment_transactions_vendor_id",
                table: "payment_transactions",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_approver_role_id",
                table: "procurement_workflow_steps",
                column: "approver_role_id");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_approver_user_id",
                table: "procurement_workflow_steps",
                column: "approver_user_id");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_procurement_workflow_id",
                table: "procurement_workflow_steps",
                column: "procurement_workflow_id");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_procurement_workflow_id_step_ord~",
                table: "procurement_workflow_steps",
                columns: new[] { "procurement_workflow_id", "step_order" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflow_steps_workflow_id",
                table: "procurement_workflow_steps",
                column: "workflow_id");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_is_active",
                table: "procurement_workflows",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_name",
                table: "procurement_workflows",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_tenant_id",
                table: "procurement_workflows",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_tenant_id_workflow_type_name",
                table: "procurement_workflows",
                columns: new[] { "tenant_id", "workflow_type", "name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_procurement_workflows_workflow_type",
                table: "procurement_workflows",
                column: "workflow_type");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_category_id",
                table: "product_definitions",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_ean",
                table: "product_definitions",
                column: "ean",
                unique: true,
                filter: "\"ean\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_gtin",
                table: "product_definitions",
                column: "gtin",
                unique: true,
                filter: "\"gtin\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_is_deleted",
                table: "product_definitions",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_sku",
                table: "product_definitions",
                column: "sku",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_product_definitions_upc",
                table: "product_definitions",
                column: "upc",
                unique: true,
                filter: "\"upc\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_products_category_id",
                schema: "public",
                table: "products",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "IX_products_is_active",
                schema: "public",
                table: "products",
                column: "is_active");

            migrationBuilder.CreateIndex(
                name: "IX_products_is_deleted",
                schema: "public",
                table: "products",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_products_name",
                schema: "public",
                table: "products",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_products_tenant_id",
                schema: "public",
                table: "products",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_products_tenant_id_product_code_unique",
                schema: "public",
                table: "products",
                columns: new[] { "tenant_id", "product_code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_projects_end_date",
                schema: "public",
                table: "projects",
                column: "end_date");

            migrationBuilder.CreateIndex(
                name: "IX_projects_is_deleted",
                schema: "public",
                table: "projects",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_projects_name",
                schema: "public",
                table: "projects",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_projects_start_date",
                schema: "public",
                table: "projects",
                column: "start_date");

            migrationBuilder.CreateIndex(
                name: "IX_projects_status",
                schema: "public",
                table: "projects",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_projects_tenant_id",
                schema: "public",
                table: "projects",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_projects_tenant_id_project_code",
                schema: "public",
                table: "projects",
                columns: new[] { "tenant_id", "project_code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_purchase_order_lines_purchase_order_id",
                table: "purchase_order_lines",
                column: "purchase_order_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_order_lines_vendor_product_id",
                table: "purchase_order_lines",
                column: "vendor_product_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_contract_id",
                table: "purchase_orders",
                column: "contract_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_order_date",
                table: "purchase_orders",
                column: "order_date");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_order_number",
                table: "purchase_orders",
                column: "order_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_requisition_id",
                table: "purchase_orders",
                column: "requisition_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_status",
                table: "purchase_orders",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_orders_vendor_id",
                table: "purchase_orders",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_DeliveryDate",
                table: "purchase_orders",
                column: "delivery_date");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_gl_account_code",
                table: "purchase_requisition_lines",
                column: "gl_account_code");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_product_definition_id",
                table: "purchase_requisition_lines",
                column: "product_definition_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_purchase_requisition_id_line_num~",
                table: "purchase_requisition_lines",
                columns: new[] { "purchase_requisition_id", "line_number" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_suggested_vendor_id",
                table: "purchase_requisition_lines",
                column: "suggested_vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_tenant_id",
                table: "purchase_requisition_lines",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisition_lines_vendor_product_id",
                table: "purchase_requisition_lines",
                column: "vendor_product_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_associated_purchase_order_id",
                table: "purchase_requisitions",
                column: "associated_purchase_order_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_request_date",
                table: "purchase_requisitions",
                column: "request_date");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_requestor_user_id",
                table: "purchase_requisitions",
                column: "requestor_user_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_status",
                table: "purchase_requisitions",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_tenant_id",
                table: "purchase_requisitions",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchase_requisitions_tenant_id_requisition_number",
                table: "purchase_requisitions",
                columns: new[] { "tenant_id", "requisition_number" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_is_deleted",
                schema: "public",
                table: "request_for_quote_lines",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_product_definition_id",
                schema: "public",
                table: "request_for_quote_lines",
                column: "product_definition_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_request_for_quote_id_line_number",
                schema: "public",
                table: "request_for_quote_lines",
                columns: new[] { "request_for_quote_id", "line_number" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_tenant_id",
                schema: "public",
                table: "request_for_quote_lines",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quote_lines_vendor_product_id",
                schema: "public",
                table: "request_for_quote_lines",
                column: "vendor_product_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_awarded_vendor_id",
                schema: "public",
                table: "request_for_quotes",
                column: "awarded_vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_created_by_user_id",
                schema: "public",
                table: "request_for_quotes",
                column: "created_by_user_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_is_deleted",
                schema: "public",
                table: "request_for_quotes",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_originating_requisition_id",
                schema: "public",
                table: "request_for_quotes",
                column: "originating_requisition_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_related_agreement_id",
                schema: "public",
                table: "request_for_quotes",
                column: "related_agreement_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_status",
                schema: "public",
                table: "request_for_quotes",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_submission_deadline",
                schema: "public",
                table: "request_for_quotes",
                column: "submission_deadline");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_tenant_id",
                schema: "public",
                table: "request_for_quotes",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_request_for_quotes_tenant_id_rfq_number",
                schema: "public",
                table: "request_for_quotes",
                columns: new[] { "tenant_id", "rfq_number" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_project_id",
                table: "requests_for_information",
                column: "project_id");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_response_deadline",
                table: "requests_for_information",
                column: "response_deadline");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_rfi_number",
                table: "requests_for_information",
                column: "rfi_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_status",
                table: "requests_for_information",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_information_tenant_id",
                table: "requests_for_information",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_awarded_contract_id",
                table: "requests_for_proposal",
                column: "awarded_contract_id");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_awarded_vendor_id",
                table: "requests_for_proposal",
                column: "awarded_vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_project_id",
                table: "requests_for_proposal",
                column: "project_id");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_rfp_number",
                table: "requests_for_proposal",
                column: "rfp_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_status",
                table: "requests_for_proposal",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_submission_deadline",
                table: "requests_for_proposal",
                column: "submission_deadline");

            migrationBuilder.CreateIndex(
                name: "IX_requests_for_proposal_tenant_id",
                table: "requests_for_proposal",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_invoice_id_invoice_line_number",
                table: "return_authorization_lines",
                columns: new[] { "invoice_id", "invoice_line_number" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_original_sales_order_line_sales_~",
                table: "return_authorization_lines",
                columns: new[] { "original_sales_order_line_sales_order_id", "original_sales_order_line_line_number" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_product_definition_id",
                table: "return_authorization_lines",
                column: "product_definition_id");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_product_id",
                table: "return_authorization_lines",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_sales_order_id_sales_order_line_~",
                table: "return_authorization_lines",
                columns: new[] { "sales_order_id", "sales_order_line_number" });

            migrationBuilder.CreateIndex(
                name: "IX_return_authorization_lines_vendor_product_id",
                table: "return_authorization_lines",
                column: "vendor_product_id");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_customer_id",
                table: "return_authorizations",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_invoice_id",
                table: "return_authorizations",
                column: "invoice_id");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_request_date",
                table: "return_authorizations",
                column: "request_date");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_rma_number",
                table: "return_authorizations",
                column: "rma_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_sales_order_id",
                table: "return_authorizations",
                column: "sales_order_id");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_status",
                table: "return_authorizations",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_return_authorizations_tenant_id",
                table: "return_authorizations",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_parent_sales_order_line_sales_order_id_pa~",
                table: "sales_order_lines",
                columns: new[] { "parent_sales_order_line_sales_order_id", "parent_sales_order_line_line_number" });

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_product_id",
                table: "sales_order_lines",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_project_id",
                table: "sales_order_lines",
                column: "project_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_project_id1",
                table: "sales_order_lines",
                column: "project_id1");

            migrationBuilder.CreateIndex(
                name: "IX_sales_order_lines_vendor_product_id",
                table: "sales_order_lines",
                column: "vendor_product_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_customer_id",
                schema: "public",
                table: "sales_orders",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_edi_transaction_reference",
                schema: "public",
                table: "sales_orders",
                column: "edi_transaction_reference");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_is_deleted",
                schema: "public",
                table: "sales_orders",
                column: "is_deleted");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_order_date",
                schema: "public",
                table: "sales_orders",
                column: "order_date");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_related_return_authorization_id",
                schema: "public",
                table: "sales_orders",
                column: "related_return_authorization_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_sales_territory_id",
                schema: "public",
                table: "sales_orders",
                column: "sales_territory_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_sales_territory_id1",
                schema: "public",
                table: "sales_orders",
                column: "sales_territory_id1");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_salesperson_id",
                schema: "public",
                table: "sales_orders",
                column: "salesperson_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_status",
                schema: "public",
                table: "sales_orders",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_tenant_id",
                schema: "public",
                table: "sales_orders",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_orders_tenant_id_order_number",
                schema: "public",
                table: "sales_orders",
                columns: new[] { "tenant_id", "order_number" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_name",
                table: "sales_territories",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_parent_territory_id",
                table: "sales_territories",
                column: "parent_territory_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_tenant_id",
                table: "sales_territories",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_sales_territories_territory_code",
                table: "sales_territories",
                column: "territory_code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sales_territory_representatives_representative_id",
                table: "sales_territory_representatives",
                column: "representative_id");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_disposition",
                table: "submittal_reviews",
                column: "disposition");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_review_date",
                table: "submittal_reviews",
                column: "review_date");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_reviewer_id",
                table: "submittal_reviews",
                column: "reviewer_id");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_technical_submittal_id",
                table: "submittal_reviews",
                column: "technical_submittal_id");

            migrationBuilder.CreateIndex(
                name: "IX_submittal_reviews_technical_submittal_id1",
                table: "submittal_reviews",
                column: "technical_submittal_id1");

            migrationBuilder.CreateIndex(
                name: "IX_suppliers_is_contract_manufacturer",
                table: "suppliers",
                column: "is_contract_manufacturer");

            migrationBuilder.CreateIndex(
                name: "IX_suppliers_risk_rating",
                table: "suppliers",
                column: "risk_rating");

            migrationBuilder.CreateIndex(
                name: "IX_suppliers_vendor_id",
                table: "suppliers",
                column: "vendor_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_contract_id",
                table: "technical_submittals",
                column: "contract_id");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_project_id",
                table: "technical_submittals",
                column: "project_id");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_project_id_submittal_number_revision",
                table: "technical_submittals",
                columns: new[] { "project_id", "submittal_number", "revision" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_purchase_order_line_id",
                table: "technical_submittals",
                column: "purchase_order_line_id");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_required_date",
                table: "technical_submittals",
                column: "required_date");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_specification_id",
                table: "technical_submittals",
                column: "specification_id");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_status",
                table: "technical_submittals",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_submittal_number",
                table: "technical_submittals",
                column: "submittal_number",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_submitted_by_user_id",
                table: "technical_submittals",
                column: "submitted_by_user_id");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_submitted_date",
                table: "technical_submittals",
                column: "submitted_date");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_tenant_id",
                table: "technical_submittals",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_technical_submittals_vendor_id",
                table: "technical_submittals",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_tenant_products_tenant_id",
                table: "tenant_products",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_tenants_identifier_unique",
                schema: "public",
                table: "tenants",
                column: "identifier",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_products_product_definition_id",
                table: "vendor_products",
                column: "product_definition_id");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_products_vendor_id_product_definition_id_unit_of_mea~",
                table: "vendor_products",
                columns: new[] { "vendor_id", "product_definition_id", "unit_of_measure", "pack_size" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_products_vendor_id_vendor_sku",
                table: "vendor_products",
                columns: new[] { "vendor_id", "vendor_sku" },
                filter: "\"vendor_sku\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_request_for_proposal_id",
                table: "vendor_proposals",
                column: "request_for_proposal_id");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_request_for_proposal_id_vendor_id",
                table: "vendor_proposals",
                columns: new[] { "request_for_proposal_id", "vendor_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_status",
                table: "vendor_proposals",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_submission_date",
                table: "vendor_proposals",
                column: "submission_date");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_tenant_id",
                table: "vendor_proposals",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_vendor_proposals_vendor_id",
                table: "vendor_proposals",
                column: "vendor_id");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_commercial_registration_number",
                table: "vendors",
                column: "commercial_registration_number");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_name",
                table: "vendors",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_status",
                table: "vendors",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_tax_id",
                table: "vendors",
                column: "tax_id");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_vat_number",
                table: "vendors",
                column: "vat_number");

            migrationBuilder.CreateIndex(
                name: "IX_vendors_vendor_code",
                table: "vendors",
                column: "vendor_code",
                unique: true,
                filter: "\"vendor_code\" IS NOT NULL");

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_delivery_notes_delivery_note_id",
                table: "delivery_note_lines",
                column: "delivery_note_id",
                principalTable: "delivery_notes",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_purchase_order_lines_purchase_order_lin~",
                table: "delivery_note_lines",
                column: "purchase_order_line_id",
                principalTable: "purchase_order_lines",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_note_lines_sales_order_lines_sales_order_id_sales_~",
                table: "delivery_note_lines",
                columns: new[] { "sales_order_id", "sales_order_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_purchase_orders_purchase_order_id",
                table: "delivery_notes",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_delivery_notes_sales_orders_sales_order_id",
                table: "delivery_notes",
                column: "sales_order_id",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_goods_receipt_notes_goods_receipt_~",
                table: "goods_receipt_note_lines",
                column: "goods_receipt_note_id",
                principalTable: "goods_receipt_notes",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_purchase_order_lines_purchase_orde~",
                table: "goods_receipt_note_lines",
                column: "purchase_order_line_id",
                principalTable: "purchase_order_lines",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_notes_purchase_orders_purchase_order_id",
                table: "goods_receipt_notes",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_invoices_invoice_id",
                table: "invoice_lines",
                column: "invoice_id",
                principalTable: "invoices",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_invoice_lines_purchase_order_lines_purchase_order_line_id",
                table: "invoice_lines",
                column: "purchase_order_line_id",
                principalTable: "purchase_order_lines",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_invoices_purchase_orders_purchase_order_id",
                table: "invoices",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_purchase_order_lines_purchase_orders_purchase_order_id",
                table: "purchase_order_lines",
                column: "purchase_order_id",
                principalTable: "purchase_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_purchase_orders_purchase_requisitions_requisition_id",
                table: "purchase_orders",
                column: "requisition_id",
                principalTable: "purchase_requisitions",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_return_authorizations_return_aut~",
                table: "return_authorization_lines",
                column: "return_authorization_id",
                principalTable: "return_authorizations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_original_sales~",
                table: "return_authorization_lines",
                columns: new[] { "original_sales_order_line_sales_order_id", "original_sales_order_line_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_sales_order_id~",
                table: "return_authorization_lines",
                columns: new[] { "sales_order_id", "sales_order_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" },
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorizations_sales_orders_sales_order_id",
                table: "return_authorizations",
                column: "sales_order_id",
                principalSchema: "public",
                principalTable: "sales_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_contracts_vendors_vendor_id",
                table: "contracts");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_vendors_vendor_id",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_purchase_orders_vendors_vendor_id",
                table: "purchase_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_invoices_purchase_orders_purchase_order_id",
                table: "invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_purchase_requisitions_purchase_orders_associated_purchase_o~",
                table: "purchase_requisitions");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorizations_sales_orders_sales_order_id",
                table: "return_authorizations");

            migrationBuilder.DropTable(
                name: "asp_net_role_claims");

            migrationBuilder.DropTable(
                name: "asp_net_user_claims");

            migrationBuilder.DropTable(
                name: "asp_net_user_logins");

            migrationBuilder.DropTable(
                name: "asp_net_user_roles");

            migrationBuilder.DropTable(
                name: "asp_net_user_tokens");

            migrationBuilder.DropTable(
                name: "budget_allocations",
                schema: "public");

            migrationBuilder.DropTable(
                name: "document_link");

            migrationBuilder.DropTable(
                name: "goods_receipt_note_lines");

            migrationBuilder.DropTable(
                name: "payment_transactions");

            migrationBuilder.DropTable(
                name: "procurement_workflow_steps");

            migrationBuilder.DropTable(
                name: "purchase_requisition_lines");

            migrationBuilder.DropTable(
                name: "request_for_quote_lines",
                schema: "public");

            migrationBuilder.DropTable(
                name: "requests_for_information");

            migrationBuilder.DropTable(
                name: "return_authorization_lines");

            migrationBuilder.DropTable(
                name: "sales_territory_representatives");

            migrationBuilder.DropTable(
                name: "submittal_reviews");

            migrationBuilder.DropTable(
                name: "suppliers");

            migrationBuilder.DropTable(
                name: "tenant_products");

            migrationBuilder.DropTable(
                name: "tenants",
                schema: "public");

            migrationBuilder.DropTable(
                name: "test_entities");

            migrationBuilder.DropTable(
                name: "vendor_proposals");

            migrationBuilder.DropTable(
                name: "asp_net_roles");

            migrationBuilder.DropTable(
                name: "budgets",
                schema: "public");

            migrationBuilder.DropTable(
                name: "departments",
                schema: "public");

            migrationBuilder.DropTable(
                name: "delivery_note_lines");

            migrationBuilder.DropTable(
                name: "goods_receipt_notes");

            migrationBuilder.DropTable(
                name: "procurement_workflows");

            migrationBuilder.DropTable(
                name: "request_for_quotes",
                schema: "public");

            migrationBuilder.DropTable(
                name: "invoice_lines");

            migrationBuilder.DropTable(
                name: "technical_submittals");

            migrationBuilder.DropTable(
                name: "requests_for_proposal");

            migrationBuilder.DropTable(
                name: "sales_order_lines");

            migrationBuilder.DropTable(
                name: "delivery_notes");

            migrationBuilder.DropTable(
                name: "asp_net_users");

            migrationBuilder.DropTable(
                name: "purchase_order_lines");

            migrationBuilder.DropTable(
                name: "products",
                schema: "public");

            migrationBuilder.DropTable(
                name: "projects",
                schema: "public");

            migrationBuilder.DropTable(
                name: "vendor_products");

            migrationBuilder.DropTable(
                name: "product_definitions");

            migrationBuilder.DropTable(
                name: "categories",
                schema: "public");

            migrationBuilder.DropTable(
                name: "vendors");

            migrationBuilder.DropTable(
                name: "purchase_orders");

            migrationBuilder.DropTable(
                name: "contracts");

            migrationBuilder.DropTable(
                name: "purchase_requisitions");

            migrationBuilder.DropTable(
                name: "sales_orders",
                schema: "public");

            migrationBuilder.DropTable(
                name: "return_authorizations");

            migrationBuilder.DropTable(
                name: "sales_territories");

            migrationBuilder.DropTable(
                name: "invoices");

            migrationBuilder.DropTable(
                name: "customers",
                schema: "public");
        }
    }
}
