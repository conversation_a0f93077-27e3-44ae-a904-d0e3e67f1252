using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using System;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// EF Core Configuration for the Category entity.
    /// </summary>
    public class CategoryConfiguration : IEntityTypeConfiguration<Category>
    {
        // Default tenant ID (must match the one in TenantConfiguration)
        public static readonly Guid DefaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");
        // Define static GUIDs for categories to ensure consistent references across configurations
        public static readonly Guid OfficeSuppliesId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid ItEquipmentId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid FurnitureId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid RawMaterialsId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid ServicesId = Guid.Parse("*************-2222-2222-************");

        // Subcategories
        public static readonly Guid PaperProductsId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid WritingInstrumentsId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid ComputersId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid PeripheralsId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid ConsultingId = Guid.Parse("*************-2222-2222-************");
        public static readonly Guid ItServicesId = Guid.Parse("*************-2222-2222-************");

        // Fixed date for seeding to ensure consistency
        private static readonly DateTime SeedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        public void Configure(EntityTypeBuilder<Category> builder)
        {
            // Table Mapping with explicit schema
            builder.ToTable("categories", "public");

            // Primary Key
            builder.HasKey(c => c.Id);
            builder.Property(c => c.Id).ValueGeneratedNever(); // For seeded entities with predefined IDs

            // --- Properties ---
            builder.Property(c => c.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(c => c.Description)
                .HasColumnType("text") // Use PostgreSQL text type for longer descriptions
                .IsRequired(false);

            builder.Property(c => c.Code)
                .HasMaxLength(50)
                .IsRequired(false);

            // PostgreSQL-specific syntax for filtered unique index
            // Note: Using snake_case column name in filter to match naming convention
            builder.HasIndex(c => c.Code)
                .IsUnique()
                .HasFilter("\"code\" IS NOT NULL")
                .HasDatabaseName("IX_categories_code_unique");

            builder.Property(c => c.UnspscCode)
                .HasMaxLength(20)
                .IsRequired(false);

            builder.HasIndex(c => c.UnspscCode)
                .HasDatabaseName("IX_categories_unspsc_code");

            // Audit fields from BaseEntity
            builder.Property("CreatedAt").IsRequired();
            builder.Property("ModifiedAt").IsRequired(false);

            // --- Tenant Isolation ---
            builder.Property(c => c.TenantId).IsRequired();
            builder.HasIndex(c => c.TenantId);

            // --- Relationships ---

            // Hierarchical Structure (Self-Referencing)
            builder.HasOne(c => c.ParentCategory)
                   .WithMany(pc => pc.ChildCategories)
                   .HasForeignKey(c => c.ParentCategoryId)
                   .IsRequired(false) // Root categories have no parent
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting parent if children exist

            // Relationship to ProductDefinitions
            builder.HasMany(c => c.ProductDefinitions)
                   .WithOne(p => p.Category)
                   .HasForeignKey(p => p.CategoryId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull);

            // Relationship to Products
            builder.HasMany(c => c.Products)
                   .WithOne(p => p.Category)
                   .HasForeignKey(p => p.CategoryId)
                   .IsRequired(false)
                   .OnDelete(DeleteBehavior.SetNull);

            // --- Seeding ---
            // Parent categories
            builder.HasData(
                new
                {
                    Id = OfficeSuppliesId,
                    TenantId = DefaultTenantId,
                    Name = "Office Supplies",
                    Description = "General office supplies and stationery",
                    Code = "OFF-SUP",
                    UnspscCode = "14111500",
                    ParentCategoryId = (Guid?)null,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = ItEquipmentId,
                    TenantId = DefaultTenantId,
                    Name = "IT Equipment",
                    Description = "Computer hardware and accessories",
                    Code = "IT-EQP",
                    UnspscCode = "43210000",
                    ParentCategoryId = (Guid?)null,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = FurnitureId,
                    TenantId = DefaultTenantId,
                    Name = "Furniture",
                    Description = "Office furniture and fixtures",
                    Code = "FURN",
                    UnspscCode = "56100000",
                    ParentCategoryId = (Guid?)null,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = RawMaterialsId,
                    TenantId = DefaultTenantId,
                    Name = "Raw Materials",
                    Description = "Materials used in manufacturing",
                    Code = "RAW-MAT",
                    UnspscCode = "11000000",
                    ParentCategoryId = (Guid?)null,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = ServicesId,
                    TenantId = DefaultTenantId,
                    Name = "Services",
                    Description = "Professional and consulting services",
                    Code = "SERV",
                    UnspscCode = "80000000",
                    ParentCategoryId = (Guid?)null,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                }
            );

            // Subcategories with parent references
            builder.HasData(
                // Office Supplies subcategories
                new
                {
                    Id = PaperProductsId,
                    TenantId = DefaultTenantId,
                    Name = "Paper Products",
                    Description = "Paper, notebooks, and notepads",
                    Code = "PAPER",
                    UnspscCode = "14111500",
                    ParentCategoryId = OfficeSuppliesId,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = WritingInstrumentsId,
                    TenantId = DefaultTenantId,
                    Name = "Writing Instruments",
                    Description = "Pens, pencils, and markers",
                    Code = "WRITE",
                    UnspscCode = "44121700",
                    ParentCategoryId = OfficeSuppliesId,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },

                // IT Equipment subcategories
                new
                {
                    Id = ComputersId,
                    TenantId = DefaultTenantId,
                    Name = "Computers",
                    Description = "Desktops, laptops, and tablets",
                    Code = "COMP",
                    UnspscCode = "43211500",
                    ParentCategoryId = ItEquipmentId,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = PeripheralsId,
                    TenantId = DefaultTenantId,
                    Name = "Peripherals",
                    Description = "Monitors, keyboards, and mice",
                    Code = "PERIPH",
                    UnspscCode = "43211900",
                    ParentCategoryId = ItEquipmentId,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },

                // Services subcategories
                new
                {
                    Id = ConsultingId,
                    TenantId = DefaultTenantId,
                    Name = "Consulting",
                    Description = "Business and management consulting",
                    Code = "CONSULT",
                    UnspscCode = "80101500",
                    ParentCategoryId = ServicesId,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                },
                new
                {
                    Id = ItServicesId,
                    TenantId = DefaultTenantId,
                    Name = "IT Services",
                    Description = "Software development and support",
                    Code = "IT-SERV",
                    UnspscCode = "81110000",
                    ParentCategoryId = ServicesId,
                    CreatedAt = SeedDate,
                    ModifiedAt = (DateTime?)null
                }
            );
        }
    }
}
