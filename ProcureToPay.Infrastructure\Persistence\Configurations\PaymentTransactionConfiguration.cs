using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProcureToPay.Domain.Entities;
using ProcureToPay.Domain.Enums; // Assuming PaymentMethod, PaymentStatus enums exist
using ProcureToPay.Infrastructure.Persistence.Extensions;

namespace ProcureToPay.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Configuration for the PaymentTransaction entity for EF Core and PostgreSQL.
    /// </summary>
    public class PaymentTransactionConfiguration : IEntityTypeConfiguration<PaymentTransaction>
    {
        public void Configure(EntityTypeBuilder<PaymentTransaction> builder)
        {
            // Table Mapping
            builder.ToTable("payment_transactions");

            // Primary Key
            builder.HasKey(pt => pt.Id);
            builder.Property(pt => pt.Id).ValueGeneratedOnAdd();

            // Properties
            builder.Property(pt => pt.TransactionReference)
                .HasMaxLength(100)
                .IsRequired(); // Required as per entity definition

            builder.Property(pt => pt.PaymentDate)
                .IsRequired()
                .HasColumnType("timestamp without time zone");

            builder.Property(pt => pt.Amount)
                .HasColumnType("numeric(18, 2)")
                .IsRequired();

            builder.Property(pt => pt.CurrencyCode)
                .IsRequired()
                .HasMaxLength(3); // ISO 4217

            builder.Property(pt => pt.PaymentMethod)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50);

            builder.Property(pt => pt.Status)
                .IsRequired()
                .HasConversion<string>() // Or int
                .HasMaxLength(50);

            builder.Property(pt => pt.BankReference)
                .HasMaxLength(100)
                .IsRequired(false);

            // Foreign Keys (Assuming Guid or appropriate types)
            builder.Property(pt => pt.VendorId).IsRequired(); // Payment is usually to a specific vendor
            builder.Property(pt => pt.InvoiceId).IsRequired(false); // Payment might cover multiple invoices or be on account

            builder.Property(pt => pt.TenantId).IsRequired();


            // --- Concurrency Token ---
            builder.UseXminAsConcurrencyToken();


            // --- Relationships ---
            // Link to Vendor (Many-to-One)
            builder.HasOne(pt => pt.Vendor)
                   .WithMany(v => v.PaymentTransactions) // Assuming Vendor has collection
                   .HasForeignKey(pt => pt.VendorId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Restrict); // Prevent deleting vendor if payments exist

            // Link to Invoice (Many-to-One, Optional)
            builder.HasOne(pt => pt.Invoice)
                   .WithMany(i => i.PaymentTransactions) // Matches Invoice config
                   .HasForeignKey(pt => pt.InvoiceId)
                   .IsRequired(false) // Payment might not be tied to one specific invoice
                   .OnDelete(DeleteBehavior.SetNull); // If invoice deleted, keep payment record but nullify link


            // --- Indexes ---
            // Unique index for TransactionReference with case-sensitive filter
            builder.HasIndex(pt => pt.TransactionReference)
                   .IsUnique()
                   .HasFilter("\"transaction_reference\" IS NOT NULL")
                   .HasDatabaseName("IX_payment_transactions_TransactionReference");

            builder.HasIndex(pt => pt.Status);
            builder.HasIndex(pt => pt.PaymentDate);
            builder.HasIndex(pt => pt.VendorId);
            builder.HasIndex(pt => pt.InvoiceId);
            builder.HasIndex(pt => pt.TenantId);


            // --- TODO ---
            // TODO: Verify FK types (VendorId, InvoiceId) match related entities.
            // TODO: If payments can apply to multiple invoices, implement a many-to-many relationship with a joining entity (e.g., PaymentAllocation).
            // TODO: Consider adding properties for payment gateway response, fees, etc.
        }
    }
}
