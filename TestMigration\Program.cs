using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProcureToPay.Domain.Interfaces;
using ProcureToPay.Infrastructure.Persistence;
using System;

namespace TestMigration
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Starting migration test application...");

            // Use the connection string with the correct port
            var connectionString = "Host=localhost;Port=60799;Database=procuretopaydb;Username=postgres;Password=localdevpassword";
            Console.WriteLine($"Connection string: {connectionString.Replace("Password=localdevpassword", "Password=***")}");

            // Create services
            var services = new ServiceCollection();

            // Add logging
            services.AddLogging(builder => builder.AddConsole());

            // Add DbContextOptions
            services.AddSingleton<DbContextOptions<ApplicationDbContext>>(provider =>
            {
                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                optionsBuilder.UseNpgsql(connectionString, sqlOptions =>
                {
                    // Add retry on failure
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 5,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToAdd: null);
                });

                return optionsBuilder.Options;
            });

            // Add tenant provider (use MigrationTenantProvider for migrations)
            services.AddScoped<ITenantProvider, MigrationTenantProvider>();

            // Configure naming convention options for migrations
            services.Configure<NamingConventionOptions>(options =>
            {
                options.EnableLogging = true; // Enable logging during migrations for debugging
            });

            // Add ApplicationDbContext
            services.AddScoped<ApplicationDbContext>((serviceProvider) =>
            {
                var options = serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>();
                var tenantProvider = serviceProvider.GetRequiredService<ITenantProvider>();
                var namingOptions = serviceProvider.GetRequiredService<IOptions<NamingConventionOptions>>();
                var logger = serviceProvider.GetRequiredService<ILogger<ApplicationDbContext>>();
                return new ApplicationDbContext(options, tenantProvider, namingOptions, logger);
            });

            // Build service provider
            var serviceProvider = services.BuildServiceProvider();

            try
            {
                // Get DbContext
                using (var scope = serviceProvider.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                    Console.WriteLine("Checking database connection...");
                    if (dbContext.Database.CanConnect())
                    {
                        Console.WriteLine("Database connection successful.");

                        Console.WriteLine("Ensuring database is created...");
                        dbContext.Database.EnsureCreated();
                        Console.WriteLine("Database creation completed.");

                        // Check if tables were created
                        Console.WriteLine("Checking if tables were created...");
                        var tables = dbContext.Database.SqlQuery<string>($"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'").ToList();
                        Console.WriteLine($"Found {tables.Count} tables in the database:");
                        foreach (var table in tables)
                        {
                            Console.WriteLine($"  - {table}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("Cannot connect to database.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("Migration test application completed.");
        }
    }
}
