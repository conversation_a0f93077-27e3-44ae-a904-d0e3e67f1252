<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProcureToPay.Infrastructure\ProcureToPay.Infrastructure.csproj" />
    <ProjectReference Include="..\ProcureToPay.Domain\ProcureToPay.Domain.csproj" />
    <ProjectReference Include="..\ProcureToPay.Application\ProcureToPay.Application.csproj" />
  </ItemGroup>

</Project>
