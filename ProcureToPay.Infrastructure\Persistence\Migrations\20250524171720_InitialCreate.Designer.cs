﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using ProcureToPay.Domain.ValueObjects;
using ProcureToPay.Infrastructure.Persistence;

#nullable disable

namespace ProcureToPay.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250524171720_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("name");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_name");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("ix_asp_net_roles_normalized_name");

                    b.ToTable("asp_net_roles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("role_id");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("asp_net_role_claims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("asp_net_user_claims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text")
                        .HasColumnName("provider_key");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text")
                        .HasColumnName("provider_display_name");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("asp_net_user_logins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.Property<string>("RoleId")
                        .HasColumnType("text")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("asp_net_user_roles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Value")
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("asp_net_user_tokens", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Budget", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AllocationRulesJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("allocation_rules_json");

                    b.Property<string>("ApprovedById")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("approved_by_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("created_by_id");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("end_date");

                    b.Property<int>("FiscalYear")
                        .HasColumnType("integer")
                        .HasColumnName("fiscal_year");

                    b.Property<string>("ForecastPeriodsJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("forecast_periods_json");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsRollingForecast")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_rolling_forecast");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("version");

                    b.Property<Guid?>("WorkflowInstanceId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_instance_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EndDate");

                    b.HasIndex("FiscalYear");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowInstanceId");

                    b.ToTable("budgets", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.BudgetAllocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("AllocationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("allocation_date");

                    b.Property<Guid>("BudgetId")
                        .HasColumnType("uuid")
                        .HasColumnName("budget_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid")
                        .HasColumnName("department_id");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("FiscalPeriodIdentifier")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("fiscal_period_identifier");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("BudgetId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("FiscalPeriodIdentifier");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("BudgetId", "FiscalPeriodIdentifier");

                    b.ToTable("budget_allocations", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Category", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<Guid?>("ParentCategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_category_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UnspscCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("unspsc_code");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_categories_code_unique")
                        .HasFilter("\"code\" IS NOT NULL");

                    b.HasIndex("ParentCategoryId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UnspscCode")
                        .HasDatabaseName("IX_categories_unspsc_code");

                    b.ToTable("categories", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "OFF-SUP",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "General office supplies and stationery",
                            Name = "Office Supplies",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "14111500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "IT-EQP",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Computer hardware and accessories",
                            Name = "IT Equipment",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "43210000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "FURN",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Office furniture and fixtures",
                            Name = "Furniture",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "56100000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "RAW-MAT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Materials used in manufacturing",
                            Name = "Raw Materials",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "11000000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "SERV",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Professional and consulting services",
                            Name = "Services",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "80000000"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "PAPER",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Paper, notebooks, and notepads",
                            Name = "Paper Products",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "14111500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "WRITE",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Pens, pencils, and markers",
                            Name = "Writing Instruments",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "44121700"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "COMP",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Desktops, laptops, and tablets",
                            Name = "Computers",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "43211500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "PERIPH",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Monitors, keyboards, and mice",
                            Name = "Peripherals",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "43211900"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "CONSULT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Business and management consulting",
                            Name = "Consulting",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "80101500"
                        },
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            Code = "IT-SERV",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Software development and support",
                            Name = "IT Services",
                            ParentCategoryId = new Guid("*************-2222-2222-************"),
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnspscCode = "81110000"
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Contract", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ComplianceDocumentLinksJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("compliance_document_links_json");

                    b.Property<string>("ContractNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("contract_number");

                    b.Property<string>("ContractType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("contract_type");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<bool>("IsAutoRenew")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_auto_renew");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("MilestonesJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("milestones_json");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("payment_terms");

                    b.Property<string>("RenewalTerms")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("renewal_terms");

                    b.Property<string>("SlaDetailsJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("sla_details_json");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TerminationPenaltyTerms")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("termination_penalty_terms");

                    b.Property<string>("TermsAndConditions")
                        .HasColumnType("text")
                        .HasColumnName("terms_and_conditions");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("title");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<decimal?>("VendorPerformanceScoreSnapshot")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)")
                        .HasColumnName("vendor_performance_score_snapshot");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("version");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ContractNumber")
                        .IsUnique();

                    b.HasIndex("EndDate");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.ToTable("contracts", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AssignedSalesRepId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("assigned_sales_rep_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CustomerCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("customer_code");

                    b.Property<string>("CustomerType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("customer_type");

                    b.Property<string>("DefaultCurrencyCode")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("default_currency_code");

                    b.Property<string>("DefaultPaymentTerms")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("default_payment_terms");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("LegalName")
                        .HasColumnType("text")
                        .HasColumnName("legal_name");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("TaxIdentifier")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("tax_identifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Website")
                        .HasColumnType("text")
                        .HasColumnName("website");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AssignedSalesRepId");

                    b.HasIndex("CustomerType");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("TaxIdentifier");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "CustomerCode")
                        .IsUnique();

                    b.ToTable("customers", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime>("DeliveryDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("delivery_date");

                    b.Property<string>("DeliveryNoteNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("delivery_note_number");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<Guid?>("PurchaseOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_id");

                    b.Property<string>("ReceivedBy")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("received_by");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_order_id");

                    b.Property<DateTime?>("ShipmentDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("shipment_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("DeliveryDate");

                    b.HasIndex("DeliveryNoteNumber")
                        .IsUnique();

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("SalesOrderId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.ToTable("delivery_notes", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNoteLine", b =>
                {
                    b.Property<Guid>("DeliveryNoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("delivery_note_id");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("line_number");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(100)
                        .HasColumnType("text")
                        .HasColumnName("batch_number");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<string>("ProductSkuSnapshot")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("product_sku_snapshot");

                    b.Property<Guid?>("PurchaseOrderLineId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_line_id");

                    b.Property<decimal>("QuantityShipped")
                        .HasColumnType("numeric(18, 4)")
                        .HasColumnName("quantity_shipped");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_order_id");

                    b.Property<int?>("SalesOrderLineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("sales_order_line_number");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(100)
                        .HasColumnType("text")
                        .HasColumnName("serial_number");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("unit_of_measure");

                    b.HasKey("DeliveryNoteId", "LineNumber");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.HasIndex("SalesOrderId", "SalesOrderLineNumber");

                    b.ToTable("delivery_note_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Code")
                        .IsUnique()
                        .HasFilter("\"Code\" IS NOT NULL");

                    b.ToTable("departments", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-4444-4444-********4401"),
                            Code = "IT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Handles IT infrastructure, software development, and technical support.",
                            Name = "Information Technology",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("*************-4444-4444-********4402"),
                            Code = "FIN",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Manages financial operations, budgeting, and accounting.",
                            Name = "Finance",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("*************-4444-4444-********4403"),
                            Code = "HR",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Handles employee recruitment, training, and personnel management.",
                            Name = "Human Resources",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("*************-4444-4444-********4404"),
                            Code = "PROC",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Manages purchasing, vendor relationships, and supply chain operations.",
                            Name = "Procurement",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("*************-4444-4444-********4405"),
                            Code = "OPS",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Oversees day-to-day operational activities and logistics.",
                            Name = "Operations",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        },
                        new
                        {
                            Id = new Guid("*************-4444-4444-********4406"),
                            Code = "MKT",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Handles advertising, brand management, and market research.",
                            Name = "Marketing",
                            TenantId = new Guid("********-1111-1111-1111-********1111")
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("DeliveryNoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("delivery_note_id");

                    b.Property<string>("GoodsReceiptNoteNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("goods_receipt_note_number");

                    b.Property<string>("GrnNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("grn_number");

                    b.Property<DateTime?>("InspectionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("inspection_date");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<Guid>("PurchaseOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_id");

                    b.Property<DateTime>("ReceiptDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("receipt_date");

                    b.Property<string>("ReceivedByName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("received_by_name");

                    b.Property<string>("ReceivedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("received_by_user_id");

                    b.Property<string>("ReceivingLocation")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("receiving_location");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("DeliveryNoteId");

                    b.HasIndex("GoodsReceiptNoteNumber")
                        .IsUnique();

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("ReceiptDate");

                    b.HasIndex("ReceivedByUserId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.ToTable("goods_receipt_notes", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNoteLine", b =>
                {
                    b.Property<Guid>("GoodsReceiptNoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("goods_receipt_note_id");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("line_number");

                    b.Property<string>("BatchNumber")
                        .HasColumnType("text")
                        .HasColumnName("batch_number");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("DeliveryNoteLineDeliveryNoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("delivery_note_line_delivery_note_id");

                    b.Property<Guid?>("DeliveryNoteLineId")
                        .HasColumnType("uuid")
                        .HasColumnName("delivery_note_line_id");

                    b.Property<int?>("DeliveryNoteLineLineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("delivery_note_line_line_number");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("expiry_date");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("InspectionCompleted")
                        .HasColumnType("boolean")
                        .HasColumnName("inspection_completed");

                    b.Property<string>("LotNumber")
                        .HasColumnType("text")
                        .HasColumnName("lot_number");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<string>("ProductDescriptionSnapshot")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("product_description_snapshot");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<string>("ProductSkuSnapshot")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("product_sku_snapshot");

                    b.Property<Guid>("PurchaseOrderLineId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_line_id");

                    b.Property<string>("PutAwayLocation")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("put_away_location");

                    b.Property<string>("QualityControlStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("quality_control_status");

                    b.Property<decimal>("QuantityAccepted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 4)")
                        .HasDefaultValue(0m)
                        .HasColumnName("quantity_accepted");

                    b.Property<decimal>("QuantityReceived")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 4)")
                        .HasDefaultValue(0m)
                        .HasColumnName("quantity_received");

                    b.Property<decimal>("QuantityRejected")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 4)")
                        .HasDefaultValue(0m)
                        .HasColumnName("quantity_rejected");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("rejection_reason");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("unit_of_measure");

                    b.HasKey("GoodsReceiptNoteId", "LineNumber");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.HasIndex("QualityControlStatus");

                    b.HasIndex("DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber");

                    b.ToTable("goods_receipt_note_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("amount_paid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid")
                        .HasColumnName("customer_id");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("due_date");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("invoice_date");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("invoice_number");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("payment_date");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("payment_terms");

                    b.Property<Guid?>("PurchaseOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_id");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<decimal>("SubTotal")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("sub_total");

                    b.Property<decimal>("Subtotal")
                        .HasColumnType("numeric(18, 2)")
                        .HasColumnName("subtotal");

                    b.Property<decimal>("TaxAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("tax_amount");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric(18, 2)")
                        .HasColumnName("total_amount");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DueDate");

                    b.HasIndex("InvoiceDate");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorId", "InvoiceNumber")
                        .IsUnique();

                    b.ToTable("invoices", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.InvoiceLine", b =>
                {
                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("line_number");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("LineTotal")
                        .HasColumnType("numeric(18, 2)")
                        .HasColumnName("line_total");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<Guid?>("PurchaseOrderLineId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_line_id");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric(18, 4)")
                        .HasColumnName("quantity");

                    b.Property<decimal?>("TaxAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("numeric(18, 2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("tax_amount");

                    b.Property<decimal?>("TaxRate")
                        .HasColumnType("numeric(5, 4)")
                        .HasColumnName("tax_rate");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("unit_of_measure");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric(18, 4)")
                        .HasColumnName("unit_price");

                    b.HasKey("InvoiceId", "LineNumber");

                    b.HasIndex("ProductId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.ToTable("invoice_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PaymentTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric(18, 2)")
                        .HasColumnName("amount");

                    b.Property<string>("BankReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("bank_reference");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("payment_date");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("payment_method");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TransactionReference")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("transaction_reference");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("PaymentDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TransactionReference")
                        .IsUnique()
                        .HasDatabaseName("IX_payment_transactions_TransactionReference")
                        .HasFilter("\"transaction_reference\" IS NOT NULL");

                    b.HasIndex("VendorId");

                    b.ToTable("payment_transactions", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflow", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("completed_date");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<int>("CurrentStatus")
                        .HasColumnType("integer")
                        .HasColumnName("current_status");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("InitiatedByName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("initiated_by_name");

                    b.Property<string>("InitiatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("initiated_by_user_id");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<DateTime>("StartedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("started_date");

                    b.Property<Guid>("SubjectDocumentId")
                        .HasColumnType("uuid")
                        .HasColumnName("subject_document_id");

                    b.Property<string>("SubjectDocumentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("subject_document_type");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int?>("Version")
                        .HasMaxLength(20)
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.Property<string>("WorkflowName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("workflow_name");

                    b.Property<string>("WorkflowType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("workflow_type");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.HasIndex("TenantId");

                    b.HasIndex("WorkflowType");

                    b.HasIndex("TenantId", "WorkflowType", "Name")
                        .IsUnique();

                    b.ToTable("procurement_workflows", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflowStep", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("ActionDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("action_date");

                    b.Property<Guid?>("ApproverRoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("approver_role_id");

                    b.Property<string>("ApproverUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("approver_user_id");

                    b.Property<DateTime?>("AssignedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("assigned_date");

                    b.Property<string>("AssigneeName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("assignee_name");

                    b.Property<string>("AssigneeUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("assignee_user_id");

                    b.Property<string>("Comments")
                        .HasColumnType("text")
                        .HasColumnName("comments");

                    b.Property<string>("ConditionExpression")
                        .HasColumnType("text")
                        .HasColumnName("condition_expression");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<Guid>("ProcurementWorkflowId")
                        .HasColumnType("uuid")
                        .HasColumnName("procurement_workflow_id");

                    b.Property<int>("SequenceOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sequence_order");

                    b.Property<TimeSpan?>("SlaDuration")
                        .HasColumnType("interval")
                        .HasColumnName("sla_duration");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("StepName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("step_name");

                    b.Property<int>("StepOrder")
                        .HasColumnType("integer")
                        .HasColumnName("step_order");

                    b.Property<Guid>("WorkflowId")
                        .HasColumnType("uuid")
                        .HasColumnName("workflow_id");

                    b.HasKey("Id");

                    b.HasIndex("ApproverRoleId");

                    b.HasIndex("ApproverUserId");

                    b.HasIndex("ProcurementWorkflowId");

                    b.HasIndex("WorkflowId");

                    b.HasIndex("ProcurementWorkflowId", "StepOrder")
                        .IsUnique();

                    b.ToTable("procurement_workflow_steps", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("ProductCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("product_code");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("unit_of_measure");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("IX_products_category_id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_products_is_active");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("IX_products_is_deleted");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_products_name");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("IX_products_tenant_id");

                    b.HasIndex("TenantId", "ProductCode")
                        .IsUnique()
                        .HasDatabaseName("IX_products_tenant_id_product_code_unique");

                    b.ToTable("products", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Premium A4 copy paper, 80gsm, 500 sheets per ream, 5 reams per box",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "A4 Copy Paper",
                            ProductCode = "PAPER-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Box"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "A5 spiral-bound notebook, 100 pages, ruled",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Spiral Notebook",
                            ProductCode = "PAPER-002",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Blue ballpoint pens, medium point, 12 pens per box",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Ballpoint Pen",
                            ProductCode = "WRITE-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Box"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "15.6\" business laptop, 16GB RAM, 512GB SSD, Intel Core i7",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Laptop Computer",
                            ProductCode = "COMP-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "24-inch LED monitor, 1080p, HDMI and DisplayPort",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "24\" Monitor",
                            ProductCode = "PERIPH-001",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        },
                        new
                        {
                            Id = new Guid("*************-3333-3333-************"),
                            CategoryId = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "Ergonomic wireless mouse, 2.4GHz, USB receiver",
                            IsActive = true,
                            IsDeleted = false,
                            Name = "Wireless Mouse",
                            ProductCode = "PERIPH-002",
                            TenantId = new Guid("********-1111-1111-1111-********1111"),
                            UnitOfMeasure = "Each"
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProductDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AttributesJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("attributes_json");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<string>("Ean")
                        .HasMaxLength(13)
                        .HasColumnType("character varying(13)")
                        .HasColumnName("ean");

                    b.Property<string>("Gtin")
                        .HasMaxLength(14)
                        .HasColumnType("character varying(14)")
                        .HasColumnName("gtin");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("LifecycleState")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Active")
                        .HasColumnName("lifecycle_state");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("name");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("sku");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Upc")
                        .HasMaxLength(12)
                        .HasColumnType("character varying(12)")
                        .HasColumnName("upc");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("version");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Ean")
                        .IsUnique()
                        .HasFilter("\"ean\" IS NOT NULL");

                    b.HasIndex("Gtin")
                        .IsUnique()
                        .HasFilter("\"gtin\" IS NOT NULL");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Sku")
                        .IsUnique();

                    b.HasIndex("Upc")
                        .IsUnique()
                        .HasFilter("\"upc\" IS NOT NULL");

                    b.ToTable("product_definitions", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Project", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_date");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<string>("ProjectCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("project_code");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("EndDate");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Name");

                    b.HasIndex("StartDate");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "ProjectCode")
                        .IsUnique();

                    b.ToTable("projects", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid")
                        .HasColumnName("contract_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<DateTime?>("DeliveryDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("delivery_date");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("order_date");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("order_number");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("payment_terms");

                    b.Property<Guid?>("RequisitionId")
                        .HasColumnType("uuid")
                        .HasColumnName("requisition_id");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("DeliveryDate")
                        .HasDatabaseName("IX_PurchaseOrder_DeliveryDate");

                    b.HasIndex("OrderDate");

                    b.HasIndex("OrderNumber")
                        .IsUnique();

                    b.HasIndex("RequisitionId");

                    b.HasIndex("Status");

                    b.HasIndex("VendorId");

                    b.ToTable("purchase_orders", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrderLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DescriptionSnapshot")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("description_snapshot");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<Guid>("PurchaseOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_id");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("quantity");

                    b.Property<string>("SkuSnapshot")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("sku_snapshot");

                    b.Property<string>("UnitOfMeasureSnapshot")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("unit_of_measure_snapshot");

                    b.Property<Guid>("VendorProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_product_id");

                    b.HasKey("Id");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("VendorProductId");

                    b.ToTable("purchase_order_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("AssociatedPurchaseOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("associated_purchase_order_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<DateTime?>("DateNeeded")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_needed");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("department");

                    b.Property<string>("Justification")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("justification");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasColumnName("notes");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("request_date");

                    b.Property<string>("RequestorEmail")
                        .IsRequired()
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("requestor_email");

                    b.Property<string>("RequestorName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("requestor_name");

                    b.Property<string>("RequestorUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("requestor_user_id");

                    b.Property<string>("RequisitionNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("requisition_number");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AssociatedPurchaseOrderId");

                    b.HasIndex("RequestDate");

                    b.HasIndex("RequestorUserId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "RequisitionNumber")
                        .IsUnique();

                    b.ToTable("purchase_requisitions", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisitionLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DateNeeded")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_needed");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("description");

                    b.Property<string>("GLAccountCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("gl_account_code");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("line_number");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("notes");

                    b.Property<Guid?>("ProductDefinitionId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_definition_id");

                    b.Property<Guid>("PurchaseRequisitionId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_requisition_id");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("quantity");

                    b.Property<Guid?>("SuggestedVendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("suggested_vendor_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("unit_of_measure");

                    b.Property<Guid?>("VendorProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_product_id");

                    b.HasKey("Id");

                    b.HasIndex("GLAccountCode");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("SuggestedVendorId");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("PurchaseRequisitionId", "LineNumber")
                        .IsUnique();

                    b.ToTable("purchase_requisition_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForInformation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime>("IssueDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("issue_date");

                    b.Property<string>("IssuedByName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("issued_by_name");

                    b.Property<string>("IssuedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("issued_by_user_id");

                    b.Property<DateTime?>("IssuedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("issued_date");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<DateTime>("ResponseDeadline")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("response_deadline");

                    b.Property<DateTime>("ResponseDueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("response_due_date");

                    b.Property<string>("RfiNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("rfi_number");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<string>("TargetAudienceDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("target_audience_description");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("title");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ResponseDeadline");

                    b.HasIndex("RfiNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.ToTable("requests_for_information", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForProposal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("AwardedContractId")
                        .HasColumnType("uuid")
                        .HasColumnName("awarded_contract_id");

                    b.Property<Guid?>("AwardedVendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("awarded_vendor_id");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("completed_date");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DecisionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("decision_date");

                    b.Property<string>("Department")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("department");

                    b.Property<string>("EvaluationCriteria")
                        .HasColumnType("jsonb")
                        .HasColumnName("evaluation_criteria");

                    b.Property<string>("ExpectedContractDuration")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("expected_contract_duration");

                    b.Property<DateTime?>("ExpectedContractStartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expected_contract_start_date");

                    b.Property<DateTime>("IssueDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("issue_date");

                    b.Property<string>("IssuedByName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("issued_by_name");

                    b.Property<string>("IssuedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("issued_by_user_id");

                    b.Property<DateTime?>("IssuedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("issued_date");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<DateTime?>("QuestionDeadline")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("question_deadline");

                    b.Property<string>("RfpNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("rfp_number");

                    b.Property<string>("ScopeOfWork")
                        .HasColumnType("text")
                        .HasColumnName("scope_of_work");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<DateTime>("SubmissionDeadline")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("submission_deadline");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("title");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AwardedContractId");

                    b.HasIndex("AwardedVendorId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("RfpNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionDeadline");

                    b.HasIndex("TenantId");

                    b.ToTable("requests_for_proposal", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("AwardedVendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("awarded_vendor_id");

                    b.Property<string>("CommunicationMethod")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("communication_method");

                    b.Property<string>("ContactPersonEmail")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("contact_person_email");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("created_by_user_id");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<Guid?>("OriginatingRequisitionId")
                        .HasColumnType("uuid")
                        .HasColumnName("originating_requisition_id");

                    b.Property<string>("RFQNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("rfq_number");

                    b.Property<Guid?>("RelatedAgreementId")
                        .HasColumnType("uuid")
                        .HasColumnName("related_agreement_id");

                    b.Property<DateTime?>("RequiredDeliveryDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("required_delivery_date");

                    b.Property<string>("ScopeOfWork")
                        .HasColumnType("text")
                        .HasColumnName("scope_of_work");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<DateTime>("SubmissionDeadline")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("submission_deadline");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TermsAndConditions")
                        .HasColumnType("text")
                        .HasColumnName("terms_and_conditions");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("title");

                    b.Property<string>("VendorReferenceInstructions")
                        .HasColumnType("text")
                        .HasColumnName("vendor_reference_instructions");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("AwardedVendorId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("OriginatingRequisitionId");

                    b.HasIndex("RelatedAgreementId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionDeadline");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "RFQNumber")
                        .IsUnique();

                    b.ToTable("request_for_quotes", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuoteLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AlternateItemProposal")
                        .HasColumnType("text")
                        .HasColumnName("alternate_item_proposal");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsSubstituteAllowed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_substitute_allowed");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("line_number");

                    b.Property<decimal?>("MinimumOrderQuantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("minimum_order_quantity");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<string>("PreferredIncoterm")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("preferred_incoterm");

                    b.Property<Guid?>("ProductDefinitionId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_definition_id");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("quantity");

                    b.Property<Guid>("RequestForQuoteId")
                        .HasColumnType("uuid")
                        .HasColumnName("request_for_quote_id");

                    b.Property<bool>("SampleRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("sample_required");

                    b.Property<string>("TechnicalSpecifications")
                        .HasColumnType("text")
                        .HasColumnName("technical_specifications");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("unit_of_measure");

                    b.Property<Guid?>("VendorProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_product_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("RequestForQuoteId", "LineNumber")
                        .IsUnique();

                    b.ToTable("request_for_quote_lines", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("AuthorizationDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("authorization_date");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid")
                        .HasColumnName("customer_id");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("expiry_date");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Notes")
                        .HasColumnType("text")
                        .HasColumnName("notes");

                    b.Property<Guid>("OriginalSalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("original_sales_order_id");

                    b.Property<string>("ReasonForReturn")
                        .HasColumnType("text")
                        .HasColumnName("reason_for_return");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("request_date");

                    b.Property<int>("RequestedAction")
                        .HasColumnType("integer")
                        .HasColumnName("requested_action");

                    b.Property<string>("RmaNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("rma_number");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_order_id");

                    b.Property<string>("ShippingInstructions")
                        .HasColumnType("text")
                        .HasColumnName("shipping_instructions");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("RequestDate");

                    b.HasIndex("RmaNumber")
                        .IsUnique();

                    b.HasIndex("SalesOrderId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.ToTable("return_authorizations", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorizationLine", b =>
                {
                    b.Property<Guid>("ReturnAuthorizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("return_authorization_id");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("line_number");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("DescriptionSnapshot")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description_snapshot");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<int?>("InvoiceLineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("invoice_line_number");

                    b.Property<string>("ItemCondition")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("item_condition");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<Guid?>("OriginalSalesOrderLineId")
                        .HasColumnType("uuid")
                        .HasColumnName("original_sales_order_line_id");

                    b.Property<int?>("OriginalSalesOrderLineLineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("original_sales_order_line_line_number");

                    b.Property<Guid?>("OriginalSalesOrderLineSalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("original_sales_order_line_sales_order_id");

                    b.Property<Guid?>("ProductDefinitionId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_definition_id");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<decimal>("QuantityAuthorized")
                        .HasColumnType("numeric(18, 4)")
                        .HasColumnName("quantity_authorized");

                    b.Property<decimal>("QuantityReceived")
                        .HasColumnType("numeric")
                        .HasColumnName("quantity_received");

                    b.Property<string>("ReasonForReturn")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("reason_for_return");

                    b.Property<int>("RequestedAction")
                        .HasColumnType("integer")
                        .HasColumnName("requested_action");

                    b.Property<Guid?>("SalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_order_id");

                    b.Property<int?>("SalesOrderLineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("sales_order_line_number");

                    b.Property<string>("SkuSnapshot")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("sku_snapshot");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("unit_of_measure");

                    b.Property<Guid?>("VendorProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_product_id");

                    b.HasKey("ReturnAuthorizationId", "LineNumber");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("ProductId");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("InvoiceId", "InvoiceLineNumber");

                    b.HasIndex("OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber");

                    b.HasIndex("SalesOrderId", "SalesOrderLineNumber");

                    b.ToTable("return_authorization_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime?>("AtpCheckDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("atp_check_date");

                    b.Property<decimal?>("CommissionRate")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)")
                        .HasColumnName("commission_rate");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("currency_code");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid")
                        .HasColumnName("customer_id");

                    b.Property<string>("EdiTransactionReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("edi_transaction_reference");

                    b.Property<bool>("IsAtpConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_atp_confirmed");

                    b.Property<bool>("IsCreditApproved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_credit_approved");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsDropShipment")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_drop_shipment");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("order_date");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("order_number");

                    b.Property<Guid?>("RelatedReturnAuthorizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("related_return_authorization_id");

                    b.Property<Guid?>("SalesTerritoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_territory_id");

                    b.Property<Guid?>("SalesTerritoryId1")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_territory_id1");

                    b.Property<string>("SalespersonId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("salesperson_id");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EdiTransactionReference");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("OrderDate");

                    b.HasIndex("RelatedReturnAuthorizationId");

                    b.HasIndex("SalesTerritoryId");

                    b.HasIndex("SalesTerritoryId1");

                    b.HasIndex("SalespersonId");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "OrderNumber")
                        .IsUnique();

                    b.ToTable("sales_orders", "public");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrderLine", b =>
                {
                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_order_id");

                    b.Property<int>("LineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("line_number");

                    b.Property<string>("AppliedDiscountDescription")
                        .HasColumnType("text")
                        .HasColumnName("applied_discount_description");

                    b.Property<string>("CostCode")
                        .HasColumnType("text")
                        .HasColumnName("cost_code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("DescriptionSnapshot")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description_snapshot");

                    b.Property<string>("DiscountAmountCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("discount_amount_currency");

                    b.Property<decimal?>("DiscountAmountValue")
                        .HasColumnType("numeric(18, 2)")
                        .HasColumnName("discount_amount_value");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsKitComponent")
                        .HasColumnType("boolean")
                        .HasColumnName("is_kit_component");

                    b.Property<decimal>("LineTotalAmount")
                        .HasColumnType("numeric(18, 2)")
                        .HasColumnName("line_total_amount");

                    b.Property<string>("LineTotalCurrency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("line_total_currency");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<Guid?>("ParentSalesOrderLineId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_sales_order_line_id");

                    b.Property<int?>("ParentSalesOrderLineLineNumber")
                        .HasColumnType("integer")
                        .HasColumnName("parent_sales_order_line_line_number");

                    b.Property<Guid?>("ParentSalesOrderLineSalesOrderId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_sales_order_line_sales_order_id");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<Guid?>("ProjectId1")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id1");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric(18, 4)")
                        .HasColumnName("quantity");

                    b.Property<decimal>("QuantityBackordered")
                        .HasColumnType("numeric")
                        .HasColumnName("quantity_backordered");

                    b.Property<DateTime?>("RequestedDeliveryDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("requested_delivery_date");

                    b.Property<string>("ReservedSerialNumbersJson")
                        .HasColumnType("text")
                        .HasColumnName("reserved_serial_numbers_json");

                    b.Property<string>("SkuSnapshot")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("sku_snapshot");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("unit_of_measure");

                    b.Property<decimal>("UnitPriceAmount")
                        .HasColumnType("numeric(18, 4)")
                        .HasColumnName("unit_price_amount");

                    b.Property<string>("UnitPriceCurrency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("unit_price_currency");

                    b.Property<Guid>("VendorProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_product_id");

                    b.Property<DateTime?>("WarrantyEndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("warranty_end_date");

                    b.HasKey("SalesOrderId", "LineNumber");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ProjectId1");

                    b.HasIndex("VendorProductId");

                    b.HasIndex("ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber");

                    b.ToTable("sales_order_lines", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesTerritory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasColumnType("text")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("name");

                    b.Property<Guid?>("ParentTerritoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_territory_id");

                    b.Property<string>("PrimarySalespersonId")
                        .HasColumnType("text")
                        .HasColumnName("primary_salesperson_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("TerritoryCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("territory_code");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("ParentTerritoryId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TerritoryCode")
                        .IsUnique();

                    b.ToTable("sales_territories", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SubmittalReview", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Comments")
                        .HasColumnType("text")
                        .HasColumnName("comments");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Disposition")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("disposition");

                    b.Property<DocumentLink>("MarkupDocument")
                        .HasColumnType("jsonb")
                        .HasColumnName("markup_document");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<int>("ReviewCycle")
                        .HasColumnType("integer")
                        .HasColumnName("review_cycle");

                    b.Property<DateTime>("ReviewDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("review_date")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid>("ReviewerGuid")
                        .HasColumnType("uuid")
                        .HasColumnName("reviewer_guid");

                    b.Property<string>("ReviewerId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("reviewer_id");

                    b.Property<string>("ReviewerName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("reviewer_name");

                    b.Property<Guid>("TechnicalSubmittalId")
                        .HasColumnType("uuid")
                        .HasColumnName("technical_submittal_id");

                    b.Property<Guid?>("TechnicalSubmittalId1")
                        .HasColumnType("uuid")
                        .HasColumnName("technical_submittal_id1");

                    b.HasKey("Id");

                    b.HasIndex("Disposition");

                    b.HasIndex("ReviewDate");

                    b.HasIndex("ReviewerId");

                    b.HasIndex("TechnicalSubmittalId");

                    b.HasIndex("TechnicalSubmittalId1");

                    b.ToTable("submittal_reviews", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int?>("AverageLeadTimeDays")
                        .HasColumnType("integer")
                        .HasColumnName("average_lead_time_days");

                    b.Property<decimal?>("CapacityUtilizationPercent")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)")
                        .HasColumnName("capacity_utilization_percent");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsContractManufacturer")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_contract_manufacturer");

                    b.Property<bool>("IsCsrCompliant")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_csr_compliant");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("RiskRating")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("risk_rating");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Active")
                        .HasColumnName("status");

                    b.Property<string>("SupplierName")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("supplier_name");

                    b.Property<decimal?>("SustainabilityScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)")
                        .HasColumnName("sustainability_score");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.HasKey("Id");

                    b.HasIndex("IsContractManufacturer");

                    b.HasIndex("RiskRating");

                    b.HasIndex("VendorId")
                        .IsUnique();

                    b.ToTable("suppliers", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TechnicalSubmittal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uuid")
                        .HasColumnName("contract_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<int>("CurrentOverallDisposition")
                        .HasColumnType("integer")
                        .HasColumnName("current_overall_disposition");

                    b.Property<int>("CurrentReviewCycle")
                        .HasColumnType("integer")
                        .HasColumnName("current_review_cycle");

                    b.Property<Guid?>("CurrentReviewerId")
                        .HasColumnType("uuid")
                        .HasColumnName("current_reviewer_id");

                    b.Property<int>("CycleCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("cycle_count");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("FinalSignOffById")
                        .HasColumnType("text")
                        .HasColumnName("final_sign_off_by_id");

                    b.Property<DateTime?>("FinalSignOffDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("final_sign_off_date");

                    b.Property<bool>("IsAsBuilt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_as_built");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsFinalDocumentation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_final_documentation");

                    b.Property<int?>("MaxResubmissions")
                        .HasColumnType("integer")
                        .HasColumnName("max_resubmissions");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<Guid?>("NonConformanceReportId")
                        .HasColumnType("uuid")
                        .HasColumnName("non_conformance_report_id");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("project_id");

                    b.Property<Guid?>("PurchaseOrderLineId")
                        .HasColumnType("uuid")
                        .HasColumnName("purchase_order_line_id");

                    b.Property<string>("RelatedITPReference")
                        .HasColumnType("text")
                        .HasColumnName("related_itp_reference");

                    b.Property<string>("RelatedNCRReference")
                        .HasColumnType("text")
                        .HasColumnName("related_ncr_reference");

                    b.Property<DateTime?>("RequiredDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("required_date");

                    b.Property<int>("ResubmissionCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("resubmission_count");

                    b.Property<DateTime?>("ReviewCompletionDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("review_completion_date");

                    b.Property<DateTime?>("ReviewDueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("review_due_date");

                    b.Property<DateTime?>("ReviewStartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("review_start_date");

                    b.Property<string>("Revision")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("revision");

                    b.Property<Guid?>("SignedOffById")
                        .HasColumnType("uuid")
                        .HasColumnName("signed_off_by_id");

                    b.Property<DateTime?>("SignedOffDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("signed_off_date");

                    b.Property<Guid?>("SpecificationId")
                        .HasColumnType("uuid")
                        .HasColumnName("specification_id");

                    b.Property<string>("SpecificationSection")
                        .HasColumnType("text")
                        .HasColumnName("specification_section");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<string>("SubmittalNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("submittal_number");

                    b.Property<string>("SubmittalType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("submittal_type");

                    b.Property<string>("SubmittedById")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("submitted_by_id");

                    b.Property<string>("SubmittedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)")
                        .HasColumnName("submitted_by_user_id");

                    b.Property<DateTime>("SubmittedDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("submitted_date");

                    b.Property<List<DocumentLink>>("SubmittedDocuments")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("submitted_documents");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid?>("TestPlanId")
                        .HasColumnType("uuid")
                        .HasColumnName("test_plan_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("title");

                    b.Property<Guid?>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("PurchaseOrderLineId");

                    b.HasIndex("RequiredDate");

                    b.HasIndex("SpecificationId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmittalNumber")
                        .IsUnique();

                    b.HasIndex("SubmittedByUserId");

                    b.HasIndex("SubmittedDate");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.HasIndex("ProjectId", "SubmittalNumber", "Revision")
                        .IsUnique();

                    b.ToTable("technical_submittals", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("address_line1");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("city");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("contact_email");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Identifier")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("identifier");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("postal_code");

                    b.Property<string>("Settings")
                        .HasColumnType("jsonb")
                        .HasColumnName("settings");

                    b.Property<string>("SubscriptionPlan")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("subscription_plan");

                    b.HasKey("Id");

                    b.HasIndex("Identifier")
                        .IsUnique()
                        .HasDatabaseName("IX_tenants_identifier_unique");

                    b.ToTable("tenants", "public");

                    b.HasData(
                        new
                        {
                            Id = new Guid("********-1111-1111-1111-********1111"),
                            ContactEmail = "<EMAIL>",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Identifier = "system",
                            IsActive = true,
                            Name = "Default System Tenant",
                            SubscriptionPlan = "Standard"
                        });
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TenantProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("price");

                    b.Property<string>("SKU")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("sku");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("tenant_products", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TestEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_date");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("name");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TestNumber")
                        .HasColumnType("integer")
                        .HasColumnName("test_number");

                    b.HasKey("Id");

                    b.ToTable("test_entities", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Vendor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CommercialRegistrationNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("commercial_registration_number");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("contact_email");

                    b.Property<string>("ContactName")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("contact_name");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)")
                        .HasColumnName("name");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("phone_number");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending")
                        .HasColumnName("status");

                    b.Property<string>("TaxId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("tax_id");

                    b.Property<string>("VatNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("vat_number");

                    b.Property<string>("VendorCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("vendor_code");

                    b.Property<string>("Website")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("website");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CommercialRegistrationNumber");

                    b.HasIndex("Name");

                    b.HasIndex("Status");

                    b.HasIndex("TaxId");

                    b.HasIndex("VatNumber");

                    b.HasIndex("VendorCode")
                        .IsUnique()
                        .HasFilter("\"vendor_code\" IS NOT NULL");

                    b.ToTable("vendors", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<int?>("LeadTimeDays")
                        .HasColumnType("integer")
                        .HasColumnName("lead_time_days");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<decimal?>("PackSize")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)")
                        .HasColumnName("pack_size");

                    b.Property<Guid>("ProductDefinitionId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_definition_id");

                    b.Property<string>("UnitOfMeasure")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("unit_of_measure");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<string>("VendorSku")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("vendor_sku");

                    b.HasKey("Id");

                    b.HasIndex("ProductDefinitionId");

                    b.HasIndex("VendorId", "VendorSku")
                        .HasFilter("\"vendor_sku\" IS NOT NULL");

                    b.HasIndex("VendorId", "ProductDefinitionId", "UnitOfMeasure", "PackSize")
                        .IsUnique();

                    b.ToTable("vendor_products", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProposal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AlternatePaymentTerms")
                        .HasColumnType("text")
                        .HasColumnName("alternate_payment_terms");

                    b.Property<string>("Comments")
                        .HasColumnType("text")
                        .HasColumnName("comments");

                    b.Property<string>("ComplianceCertificationsJson")
                        .HasColumnType("text")
                        .HasColumnName("compliance_certifications_json");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("PerformanceGuarantees")
                        .HasColumnType("text")
                        .HasColumnName("performance_guarantees");

                    b.Property<Guid>("RequestForProposalId")
                        .HasColumnType("uuid")
                        .HasColumnName("request_for_proposal_id");

                    b.Property<string>("RiskSharingClauses")
                        .HasColumnType("text")
                        .HasColumnName("risk_sharing_clauses");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("status");

                    b.Property<string>("SubcontractorDisclosures")
                        .HasColumnType("text")
                        .HasColumnName("subcontractor_disclosures");

                    b.Property<DateTime>("SubmissionDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("submission_date");

                    b.Property<string>("SustainabilityCommitments")
                        .HasColumnType("text")
                        .HasColumnName("sustainability_commitments");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<decimal?>("TotalProposedValue")
                        .HasColumnType("numeric(18, 2)")
                        .HasColumnName("total_proposed_value");

                    b.Property<DateTime?>("ValidityEndDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("validity_end_date");

                    b.Property<int>("ValidityPeriodDays")
                        .HasColumnType("integer")
                        .HasColumnName("validity_period_days");

                    b.Property<string>("ValueAddedServices")
                        .HasColumnType("text")
                        .HasColumnName("value_added_services");

                    b.Property<Guid>("VendorId")
                        .HasColumnType("uuid")
                        .HasColumnName("vendor_id");

                    b.Property<int>("Version")
                        .HasColumnType("integer")
                        .HasColumnName("version");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("RequestForProposalId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmissionDate");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.HasIndex("RequestForProposalId", "VendorId")
                        .IsUnique();

                    b.ToTable("vendor_proposals", (string)null);
                });

            modelBuilder.Entity("ProcureToPay.Domain.ValueObjects.DocumentLink", b =>
                {
                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("document_type");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("url");

                    b.ToTable("document_link");
                });

            modelBuilder.Entity("ProcureToPay.Infrastructure.Identity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer")
                        .HasColumnName("access_failed_count");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("email");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("email_confirmed");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("lockout_enabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("lockout_end");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_email");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_user_name");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text")
                        .HasColumnName("password_hash");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("phone_number");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("phone_number_confirmed");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text")
                        .HasColumnName("security_stamp");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("two_factor_enabled");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("user_name");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("ix_asp_net_users_normalized_email");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("ix_asp_net_users_normalized_user_name");

                    b.ToTable("asp_net_users", (string)null);
                });

            modelBuilder.Entity("sales_territory_representatives", b =>
                {
                    b.Property<Guid>("SalesTerritoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("sales_territory_id");

                    b.Property<string>("RepresentativeId")
                        .HasColumnType("text")
                        .HasColumnName("representative_id");

                    b.HasKey("SalesTerritoryId", "RepresentativeId");

                    b.HasIndex("RepresentativeId");

                    b.ToTable("sales_territory_representatives", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Budget", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "BaselineAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 4)")
                                .HasColumnName("baseline_amount");

                            b1.HasKey("BudgetId");

                            b1.ToTable("budgets", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "ForecastAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 4)")
                                .HasColumnName("forecast_amount");

                            b1.HasKey("BudgetId");

                            b1.ToTable("budgets", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetId");
                        });

                    b.Navigation("BaselineAmount")
                        .IsRequired();

                    b.Navigation("ForecastAmount");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.BudgetAllocation", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Budget", "Budget")
                        .WithMany("BudgetAllocations")
                        .HasForeignKey("BudgetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Department", "Department")
                        .WithMany("BudgetAllocations")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "AllocatedAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetAllocationId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 2)")
                                .HasColumnName("allocated_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("allocated_currency_code");

                            b1.HasKey("BudgetAllocationId");

                            b1.ToTable("budget_allocations", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetAllocationId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "ConsumedAmount", b1 =>
                        {
                            b1.Property<Guid>("BudgetAllocationId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("numeric(18, 2)")
                                .HasDefaultValue(0m)
                                .HasColumnName("consumed_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("consumed_currency_code");

                            b1.HasKey("BudgetAllocationId");

                            b1.ToTable("budget_allocations", "public");

                            b1.WithOwner()
                                .HasForeignKey("BudgetAllocationId");
                        });

                    b.Navigation("AllocatedAmount")
                        .IsRequired();

                    b.Navigation("Budget");

                    b.Navigation("ConsumedAmount")
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Category", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Category", "ParentCategory")
                        .WithMany("ChildCategories")
                        .HasForeignKey("ParentCategoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Contract", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("Contracts")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalContractValue", b1 =>
                        {
                            b1.Property<Guid>("ContractId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("TotalContractValueAmount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("TotalContractValueCurrencyCode");

                            b1.HasKey("ContractId");

                            b1.ToTable("contracts");

                            b1.WithOwner()
                                .HasForeignKey("ContractId");
                        });

                    b.Navigation("TotalContractValue");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Customer", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("billing_street");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "CreditLimit", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric(18, 2)")
                                .HasColumnName("credit_limit_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("credit_limit_currency_code");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.ContactPerson", "PrimaryContact", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Email")
                                .HasMaxLength(254)
                                .HasColumnType("character varying(254)")
                                .HasColumnName("primary_contact_email");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(150)
                                .HasColumnType("character varying(150)")
                                .HasColumnName("primary_contact_name");

                            b1.Property<string>("Phone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("primary_contact_phone");

                            b1.Property<string>("Role")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("primary_contact_role");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("CustomerId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("CustomerId");

                            b1.ToTable("customers", "public");

                            b1.WithOwner()
                                .HasForeignKey("CustomerId");
                        });

                    b.Navigation("BillingAddress")
                        .IsRequired();

                    b.Navigation("CreditLimit");

                    b.Navigation("PrimaryContact")
                        .IsRequired();

                    b.Navigation("ShippingAddress")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNote", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("DeliveryNotes")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrder", "SalesOrder")
                        .WithMany()
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("DeliveryNoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_city");

                            b1.Property<string>("Country")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_country");

                            b1.Property<string>("PostalCode")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_postal_code");

                            b1.Property<string>("State")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_state");

                            b1.Property<string>("Street")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("billing_street");

                            b1.HasKey("DeliveryNoteId");

                            b1.ToTable("delivery_notes");

                            b1.WithOwner()
                                .HasForeignKey("DeliveryNoteId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("DeliveryNoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("DeliveryNoteId");

                            b1.ToTable("delivery_notes");

                            b1.WithOwner()
                                .HasForeignKey("DeliveryNoteId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.CarrierInformation", "CarrierInfo", b1 =>
                        {
                            b1.Property<Guid>("DeliveryNoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Name")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("carrier_name");

                            b1.Property<string>("TrackingNumber")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("tracking_number");

                            b1.HasKey("DeliveryNoteId");

                            b1.ToTable("delivery_notes");

                            b1.WithOwner()
                                .HasForeignKey("DeliveryNoteId");
                        });

                    b.Navigation("BillingAddress");

                    b.Navigation("CarrierInfo");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("SalesOrder");

                    b.Navigation("ShippingAddress");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNoteLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.DeliveryNote", "DeliveryNote")
                        .WithMany("Lines")
                        .HasForeignKey("DeliveryNoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany("DeliveryNoteLines")
                        .HasForeignKey("PurchaseOrderLineId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "SalesOrderLine")
                        .WithMany()
                        .HasForeignKey("SalesOrderId", "SalesOrderLineNumber")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("DeliveryNote");

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrderLine");

                    b.Navigation("SalesOrderLine");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNote", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.DeliveryNote", "DeliveryNote")
                        .WithMany()
                        .HasForeignKey("DeliveryNoteId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("GoodsReceiptNotes")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("ReceivedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeliveryNote");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNoteLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.GoodsReceiptNote", "GoodsReceiptNote")
                        .WithMany("Lines")
                        .HasForeignKey("GoodsReceiptNoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany("GoodsReceiptNoteLines")
                        .HasForeignKey("PurchaseOrderLineId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.DeliveryNoteLine", "DeliveryNoteLine")
                        .WithMany()
                        .HasForeignKey("DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber");

                    b.Navigation("DeliveryNoteLine");

                    b.Navigation("GoodsReceiptNote");

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrderLine");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Invoice", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Customer", "Customer")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("Invoices")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("Invoices")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.InvoiceLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Invoice", "Invoice")
                        .WithMany("Lines")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany("InvoiceLines")
                        .HasForeignKey("PurchaseOrderLineId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Invoice");

                    b.Navigation("Product");

                    b.Navigation("PurchaseOrderLine");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PaymentTransaction", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Invoice", "Invoice")
                        .WithMany("PaymentTransactions")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("PaymentTransactions")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Invoice");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflowStep", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("ApproverUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.ProcurementWorkflow", "ProcurementWorkflow")
                        .WithMany("Steps")
                        .HasForeignKey("ProcurementWorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ProcurementWorkflow", "Workflow")
                        .WithMany()
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProcurementWorkflow");

                    b.Navigation("Workflow");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Product", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProductDefinition", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Category", "Category")
                        .WithMany("ProductDefinitions")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Project", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "BudgetAmount", b1 =>
                        {
                            b1.Property<Guid>("ProjectId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("BudgetAmount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("BudgetCurrencyCode");

                            b1.HasKey("ProjectId");

                            b1.ToTable("projects", "public");

                            b1.WithOwner()
                                .HasForeignKey("ProjectId");
                        });

                    b.Navigation("BudgetAmount");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrder", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "Contract")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseRequisition", "Requisition")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("RequisitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShipmentAddress", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("Shipment_City");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("Shipment_Country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("Shipment_PostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("Shipment_State");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("Shipment_Street");

                            b1.HasKey("PurchaseOrderId");

                            b1.ToTable("purchase_orders");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("TotalAmount");

                            b1.HasKey("PurchaseOrderId");

                            b1.ToTable("purchase_orders");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderId");
                        });

                    b.Navigation("Contract");

                    b.Navigation("Requisition");

                    b.Navigation("ShipmentAddress")
                        .IsRequired();

                    b.Navigation("TotalAmount")
                        .IsRequired();

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrderLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("Lines")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany("PurchaseOrderLines")
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "LineTotal", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("LineTotalAmount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("LineTotalCurrencyCode");

                            b1.HasKey("PurchaseOrderLineId");

                            b1.ToTable("purchase_order_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderLineId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "UnitPriceSnapshot", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("UnitPriceAmount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("UnitPriceCurrencyCode");

                            b1.HasKey("PurchaseOrderLineId");

                            b1.ToTable("purchase_order_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderLineId");
                        });

                    b.Navigation("LineTotal")
                        .IsRequired();

                    b.Navigation("PurchaseOrder");

                    b.Navigation("UnitPriceSnapshot")
                        .IsRequired();

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisition", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrder", "AssociatedPurchaseOrder")
                        .WithMany()
                        .HasForeignKey("AssociatedPurchaseOrderId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("Shipping_City");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("Shipping_Country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("Shipping_PostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("Shipping_State");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("Shipping_Street");

                            b1.HasKey("PurchaseRequisitionId");

                            b1.ToTable("purchase_requisitions");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalEstimatedCost", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("TotalEstimatedCostAmount");

                            b1.HasKey("PurchaseRequisitionId");

                            b1.ToTable("purchase_requisitions");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionId");
                        });

                    b.Navigation("AssociatedPurchaseOrder");

                    b.Navigation("ShippingAddress");

                    b.Navigation("TotalEstimatedCost")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisitionLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany()
                        .HasForeignKey("ProductDefinitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseRequisition", "PurchaseRequisition")
                        .WithMany("Lines")
                        .HasForeignKey("PurchaseRequisitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "SuggestedVendor")
                        .WithMany()
                        .HasForeignKey("SuggestedVendorId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "EstimatedLineCost", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("EstLineCostAmount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("EstLineCostCurrencyCode");

                            b1.HasKey("PurchaseRequisitionLineId");

                            b1.ToTable("purchase_requisition_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionLineId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "EstimatedUnitPrice", b1 =>
                        {
                            b1.Property<Guid>("PurchaseRequisitionLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("EstUnitPriceAmount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("EstUnitPriceCurrencyCode");

                            b1.HasKey("PurchaseRequisitionLineId");

                            b1.ToTable("purchase_requisition_lines");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseRequisitionLineId");
                        });

                    b.Navigation("EstimatedLineCost");

                    b.Navigation("EstimatedUnitPrice");

                    b.Navigation("ProductDefinition");

                    b.Navigation("PurchaseRequisition");

                    b.Navigation("SuggestedVendor");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForInformation", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany("RequestsForInformation")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Project");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForProposal", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "AwardedContract")
                        .WithMany()
                        .HasForeignKey("AwardedContractId");

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "AwardedVendor")
                        .WithMany()
                        .HasForeignKey("AwardedVendorId");

                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany("RequestsForProposal")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AwardedContract");

                    b.Navigation("AwardedVendor");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuote", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "AwardedVendor")
                        .WithMany()
                        .HasForeignKey("AwardedVendorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseRequisition", "OriginatingRequisition")
                        .WithMany()
                        .HasForeignKey("OriginatingRequisitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "RelatedAgreement")
                        .WithMany()
                        .HasForeignKey("RelatedAgreementId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "DeliverToAddress", b1 =>
                        {
                            b1.Property<Guid>("RequestForQuoteId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("deliver_to_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("deliver_to_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("deliver_to_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("deliver_to_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("deliver_to_street");

                            b1.HasKey("RequestForQuoteId");

                            b1.ToTable("request_for_quotes", "public");

                            b1.WithOwner()
                                .HasForeignKey("RequestForQuoteId");
                        });

                    b.Navigation("AwardedVendor");

                    b.Navigation("DeliverToAddress")
                        .IsRequired();

                    b.Navigation("OriginatingRequisition");

                    b.Navigation("RelatedAgreement");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuoteLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany()
                        .HasForeignKey("ProductDefinitionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.RequestForQuote", "RequestForQuote")
                        .WithMany("Lines")
                        .HasForeignKey("RequestForQuoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "EstimatedTcoValue", b1 =>
                        {
                            b1.Property<Guid>("RequestForQuoteLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("est_tco_value_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("est_tco_value_currency_code");

                            b1.HasKey("RequestForQuoteLineId");

                            b1.ToTable("request_for_quote_lines", "public");

                            b1.WithOwner()
                                .HasForeignKey("RequestForQuoteLineId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TargetUnitPrice", b1 =>
                        {
                            b1.Property<Guid>("RequestForQuoteLineId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("target_unit_price_amount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("target_unit_price_currency_code");

                            b1.HasKey("RequestForQuoteLineId");

                            b1.ToTable("request_for_quote_lines", "public");

                            b1.WithOwner()
                                .HasForeignKey("RequestForQuoteLineId");
                        });

                    b.Navigation("EstimatedTcoValue");

                    b.Navigation("ProductDefinition");

                    b.Navigation("RequestForQuote");

                    b.Navigation("TargetUnitPrice");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorization", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Customer", "Customer")
                        .WithMany("ReturnAuthorizations")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Invoice", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrder", "SalesOrder")
                        .WithMany()
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Customer");

                    b.Navigation("Invoice");

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorizationLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany()
                        .HasForeignKey("ProductDefinitionId");

                    b.HasOne("ProcureToPay.Domain.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.ReturnAuthorization", "ReturnAuthorization")
                        .WithMany("Lines")
                        .HasForeignKey("ReturnAuthorizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId");

                    b.HasOne("ProcureToPay.Domain.Entities.InvoiceLine", "InvoiceLine")
                        .WithMany()
                        .HasForeignKey("InvoiceId", "InvoiceLineNumber")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "OriginalSalesOrderLine")
                        .WithMany()
                        .HasForeignKey("OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber");

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "SalesOrderLine")
                        .WithMany()
                        .HasForeignKey("SalesOrderId", "SalesOrderLineNumber")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("InvoiceLine");

                    b.Navigation("OriginalSalesOrderLine");

                    b.Navigation("Product");

                    b.Navigation("ProductDefinition");

                    b.Navigation("ReturnAuthorization");

                    b.Navigation("SalesOrderLine");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrder", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Customer", "Customer")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProcureToPay.Domain.Entities.ReturnAuthorization", "ReturnAuthorization")
                        .WithMany()
                        .HasForeignKey("RelatedReturnAuthorizationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", "SalesTerritory")
                        .WithMany()
                        .HasForeignKey("SalesTerritoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", null)
                        .WithMany("SalesOrders")
                        .HasForeignKey("SalesTerritoryId1");

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("billing_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("billing_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("billing_street");

                            b1.HasKey("SalesOrderId");

                            b1.ToTable("sales_orders", "public");

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "ShippingAddress", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("shipping_postal_code");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("shipping_state");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("shipping_street");

                            b1.HasKey("SalesOrderId");

                            b1.ToTable("sales_orders", "public");

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId");
                        });

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "TotalAmount", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("total_amount");

                            b1.HasKey("SalesOrderId");

                            b1.ToTable("sales_orders", "public");

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId");
                        });

                    b.Navigation("BillingAddress")
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("ReturnAuthorization");

                    b.Navigation("SalesTerritory");

                    b.Navigation("ShippingAddress")
                        .IsRequired();

                    b.Navigation("TotalAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrderLine", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Project", null)
                        .WithMany("SalesOrderLines")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId1");

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrder", "SalesOrder")
                        .WithMany("Lines")
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.VendorProduct", "VendorProduct")
                        .WithMany()
                        .HasForeignKey("VendorProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.SalesOrderLine", "ParentSalesOrderLine")
                        .WithMany("ChildSalesOrderLines")
                        .HasForeignKey("ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber");

                    b.Navigation("ParentSalesOrderLine");

                    b.Navigation("Product");

                    b.Navigation("Project");

                    b.Navigation("SalesOrder");

                    b.Navigation("VendorProduct");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesTerritory", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", "ParentTerritory")
                        .WithMany("ChildTerritories")
                        .HasForeignKey("ParentTerritoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentTerritory");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SubmittalReview", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("ReviewerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.TechnicalSubmittal", "TechnicalSubmittal")
                        .WithMany("Reviews")
                        .HasForeignKey("TechnicalSubmittalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.TechnicalSubmittal", null)
                        .WithMany("ReviewHistory")
                        .HasForeignKey("TechnicalSubmittalId1")
                        .HasConstraintName("FK_submittal_reviews_technical_submittals_technical_submittal~1");

                    b.Navigation("TechnicalSubmittal");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Supplier", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithOne("SupplierProfile")
                        .HasForeignKey("ProcureToPay.Domain.Entities.Supplier", "VendorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("ProcureToPay.Domain.ValueObjects.ContactPerson", "EmergencyContacts", b1 =>
                        {
                            b1.Property<Guid>("SupplierId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Email")
                                .HasColumnType("text");

                            b1.Property<int?>("HierarchyLevel")
                                .HasColumnType("integer");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.Property<string>("Phone")
                                .HasColumnType("text");

                            b1.Property<string>("Role")
                                .HasColumnType("text");

                            b1.HasKey("SupplierId", "__synthesizedOrdinal");

                            b1.ToTable("suppliers");

                            b1.ToJson("EmergencyContacts");

                            b1.WithOwner()
                                .HasForeignKey("SupplierId");
                        });

                    b.Navigation("EmergencyContacts");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TechnicalSubmittal", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId");

                    b.HasOne("ProcureToPay.Domain.Entities.Project", "Project")
                        .WithMany("TechnicalSubmittals")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.PurchaseOrderLine", "PurchaseOrderLine")
                        .WithMany()
                        .HasForeignKey("PurchaseOrderLineId");

                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("SubmittedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Contract");

                    b.Navigation("Project");

                    b.Navigation("PurchaseOrderLine");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Vendor", b =>
                {
                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Address", "PrimaryAddress", b1 =>
                        {
                            b1.Property<Guid>("VendorId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PrimaryAddress_City");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PrimaryAddress_Country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("PrimaryAddress_PostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PrimaryAddress_State");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("PrimaryAddress_Street");

                            b1.HasKey("VendorId");

                            b1.ToTable("vendors");

                            b1.WithOwner()
                                .HasForeignKey("VendorId");
                        });

                    b.Navigation("PrimaryAddress")
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProduct", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.ProductDefinition", "ProductDefinition")
                        .WithMany("VendorProducts")
                        .HasForeignKey("ProductDefinitionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("VendorProducts")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("ProcureToPay.Domain.ValueObjects.Money", "UnitPrice", b1 =>
                        {
                            b1.Property<Guid>("VendorProductId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 4)
                                .HasColumnType("numeric(18,4)")
                                .HasColumnName("UnitPriceAmount");

                            b1.Property<string>("CurrencyCode")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("UnitPriceCurrencyCode");

                            b1.HasKey("VendorProductId");

                            b1.ToTable("vendor_products");

                            b1.WithOwner()
                                .HasForeignKey("VendorProductId");
                        });

                    b.Navigation("ProductDefinition");

                    b.Navigation("UnitPrice")
                        .IsRequired();

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProposal", b =>
                {
                    b.HasOne("ProcureToPay.Domain.Entities.RequestForProposal", "RequestForProposal")
                        .WithMany("VendorProposals")
                        .HasForeignKey("RequestForProposalId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.Vendor", "Vendor")
                        .WithMany("VendorProposals")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("RequestForProposal");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("sales_territory_representatives", b =>
                {
                    b.HasOne("ProcureToPay.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("RepresentativeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProcureToPay.Domain.Entities.SalesTerritory", null)
                        .WithMany()
                        .HasForeignKey("SalesTerritoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Budget", b =>
                {
                    b.Navigation("BudgetAllocations");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Category", b =>
                {
                    b.Navigation("ChildCategories");

                    b.Navigation("ProductDefinitions");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Contract", b =>
                {
                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Customer", b =>
                {
                    b.Navigation("Invoices");

                    b.Navigation("ReturnAuthorizations");

                    b.Navigation("SalesOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.DeliveryNote", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Department", b =>
                {
                    b.Navigation("BudgetAllocations");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.GoodsReceiptNote", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Invoice", b =>
                {
                    b.Navigation("Lines");

                    b.Navigation("PaymentTransactions");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProcurementWorkflow", b =>
                {
                    b.Navigation("Steps");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ProductDefinition", b =>
                {
                    b.Navigation("VendorProducts");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Project", b =>
                {
                    b.Navigation("RequestsForInformation");

                    b.Navigation("RequestsForProposal");

                    b.Navigation("SalesOrderLines");

                    b.Navigation("TechnicalSubmittals");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrder", b =>
                {
                    b.Navigation("DeliveryNotes");

                    b.Navigation("GoodsReceiptNotes");

                    b.Navigation("Invoices");

                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseOrderLine", b =>
                {
                    b.Navigation("DeliveryNoteLines");

                    b.Navigation("GoodsReceiptNoteLines");

                    b.Navigation("InvoiceLines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.PurchaseRequisition", b =>
                {
                    b.Navigation("Lines");

                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForProposal", b =>
                {
                    b.Navigation("VendorProposals");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.RequestForQuote", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.ReturnAuthorization", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrder", b =>
                {
                    b.Navigation("Lines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesOrderLine", b =>
                {
                    b.Navigation("ChildSalesOrderLines");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.SalesTerritory", b =>
                {
                    b.Navigation("ChildTerritories");

                    b.Navigation("SalesOrders");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.TechnicalSubmittal", b =>
                {
                    b.Navigation("ReviewHistory");

                    b.Navigation("Reviews");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.Vendor", b =>
                {
                    b.Navigation("Contracts");

                    b.Navigation("Invoices");

                    b.Navigation("PaymentTransactions");

                    b.Navigation("PurchaseOrders");

                    b.Navigation("SupplierProfile");

                    b.Navigation("VendorProducts");

                    b.Navigation("VendorProposals");
                });

            modelBuilder.Entity("ProcureToPay.Domain.Entities.VendorProduct", b =>
                {
                    b.Navigation("PurchaseOrderLines");
                });
#pragma warning restore 612, 618
        }
    }
}
